{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\ConfirmDialog.vue", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\game\\components\\ConfirmDialog.vue", "mtime": 1749742934824}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./ConfirmDialog.vue?vue&type=template&id=64e22abc&scoped=true\"\nimport script from \"./ConfirmDialog.vue?vue&type=script&lang=js\"\nexport * from \"./ConfirmDialog.vue?vue&type=script&lang=js\"\nimport style0 from \"./ConfirmDialog.vue?vue&type=style&index=0&id=64e22abc&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"64e22abc\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\teachingproject\\\\teaching\\\\web\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('64e22abc')) {\n      api.createRecord('64e22abc', component.options)\n    } else {\n      api.reload('64e22abc', component.options)\n    }\n    module.hot.accept(\"./ConfirmDialog.vue?vue&type=template&id=64e22abc&scoped=true\", function () {\n      api.rerender('64e22abc', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/game/components/ConfirmDialog.vue\"\nexport default component.exports"]}