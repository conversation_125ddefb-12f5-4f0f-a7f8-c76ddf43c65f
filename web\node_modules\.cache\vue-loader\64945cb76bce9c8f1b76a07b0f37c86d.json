{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\SlidingPanel.vue?vue&type=style&index=0&id=5316d9a4&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\home\\modules\\SlidingPanel.vue", "mtime": 1749719933806}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n/* 定义儿童友好的色彩主题 */\n:root {\n  --primary-color: #4285F4;\n  --secondary-color: #34A853;\n  --accent-color: #FBBC05;\n  --accent2-color: #EA4335;\n  --light-color: #E8F0FE;\n  --dark-color: #1A73E8;\n}\n\n/* 滑动面板容器 - 固定在屏幕右侧，垂直居中偏上位置 */\n.sliding-panel-container {\n  position: fixed;  /* 固定定位，不随页面滚动 */\n  right: 0;         /* 贴靠屏幕右侧 */\n  top: 80%;      /* 位于屏幕80%高度处，更靠下 */\n  transform: translateY(-50%); /* 垂直居中调整 */\n  z-index: 999;     /* 确保在其他元素上层显示 */\n}\n\n/* 对话泡泡容器 - 少儿编程风格 */\n.tooltip-container {\n  position: absolute; /* 绝对定位，相对于父元素 */\n  top: 0;            /* 顶部对齐 */\n  right: 55px;       /* 距离右侧55像素 */\n  z-index: 1001;     /* 确保在面板上层显示 */\n}\n\n/* 少儿编程风格的对话泡泡 */\n.kid-friendly-bubble {\n  position: relative;   /* 相对定位，便于添加伪元素 */\n  display: inline-block; /* 行内块元素 */\n  padding: 12px 20px;    /* 内边距 */\n  background: linear-gradient(135deg, #42b883, #35495e); /* 渐变背景 */\n  border-radius: 18px;   /* 圆角边框 */\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 0 6px rgba(66, 184, 131, 0.4); /* 双层阴影效果 */\n  color: white;          /* 文字颜色 */\n  font-size: 15px;       /* 文字大小 */\n  width: auto;           /* 自适应宽度 */\n  white-space: nowrap;   /* 文本不换行 */\n  text-align: center;    /* 文本居中 */\n  cursor: pointer;       /* 鼠标指针样式 */\n  transform: translateY(-50%); /* 垂直居中调整 */\n  animation: float 3s ease-in-out infinite; /* 浮动动画 */\n  border: 2px solid white; /* 白色边框 */\n  font-weight: bold;     /* 文字加粗 */\n  \n  .bubble-content {\n    position: relative; /* 相对定位 */\n  }\n\n  .stars1 {\n    position: absolute; /* 绝对定位 */\n    top: 2px;         /* 顶部偏移 */\n    right: 122px;       /* 右侧偏移 */\n    font-size: 14px;    /* 星星大小 */\n    color: #FBBC05;     /* 星星颜色 */\n    animation: twinkle 1.5s ease-in-out infinite alternate; /* 闪烁动画 */\n    \n    .star1:nth-child(2) {\n      animation-delay: 0.7s; /* 第二个星星动画延迟 */\n      margin-left: 5px;      /* 左侧间距 */\n    }\n  }\n  \n  .stars2 {\n    position: absolute; /* 绝对定位 */\n    top: 2px;         /* 顶部偏移 */\n    right: -17px;       /* 右侧偏移 */\n    font-size: 14px;    /* 星星大小 */\n    color: #FBBC05;     /* 星星颜色 */\n    animation: twinkle 1.5s ease-in-out infinite alternate; /* 闪烁动画 */\n    \n    .star2:nth-child(2) {\n      animation-delay: 0.7s; /* 第二个星星动画延迟 */\n      margin-left: 5px;      /* 左侧间距 */\n    }\n  }\n}\n\n/* 三角形指向 - 动画版 */\n.kid-friendly-bubble:after {\n  content: '';          /* 必须的内容属性 */\n  position: absolute;   /* 绝对定位 */\n  top: 50%;             /* 垂直居中 */\n  right: -12px;         /* 右侧偏移 */\n  margin-top: -10px;    /* 微调垂直位置 */\n  border-width: 10px 0 10px 12px; /* 三角形边框宽度 */\n  border-style: solid;  /* 边框样式 */\n  border-color: transparent transparent transparent #35495e; /* 只显示左边框形成三角形 */\n  animation: pulse 2s infinite; /* 脉冲动画 */\n}\n\n/* 面向中小学生的侧边面板 */\n.kid-friendly-panel {\n  display: flex;        /* 弹性布局 */\n  align-items: center;  /* 垂直居中 */\n  background: linear-gradient(to bottom, #ffffff, #f0f8ff); /* 渐变背景 */\n  border-radius: 20px 0 0 20px; /* 左侧圆角 */\n  box-shadow: -4px 4px 15px rgba(0, 0, 0, 0.15); /* 阴影效果 */\n  overflow: hidden;     /* 隐藏溢出内容 */\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* 平滑过渡效果 */\n  height: 180px;        /* 固定高度 */\n  cursor: pointer;      /* 鼠标指针样式 */\n  border: 2px solid #e0e0e0; /* 边框 */\n  \n  &.expanded {\n    width: 320px;       /* 展开时的宽度 */\n    box-shadow: -6px 6px 20px rgba(0, 0, 0, 0.2); /* 展开时更强的阴影 */\n  }\n  \n  &:hover {\n    transform: translateX(-5px); /* 悬停时向左移动 */\n  }\n}\n\n/* 程序员风格的图标区域 */\n.panel-avatar {\n  width: 50px;          /* 固定宽度 */\n  height: 180px;        /* 与面板同高 */\n  display: flex;        /* 弹性布局 */\n  align-items: center;  /* 垂直居中 */\n  justify-content: center; /* 水平居中 */\n  background: linear-gradient(135deg, #4285F4, #0F9D58); /* 谷歌风格渐变 */\n  position: relative;   /* 相对定位 */\n  overflow: hidden;     /* 隐藏溢出内容 */\n  \n  &:before {\n    content: \"\";        /* 必须的内容属性 */\n    position: absolute; /* 绝对定位 */\n    top: 0;             /* 顶部对齐 */\n    left: 0;            /* 左侧对齐 */\n    right: 0;           /* 右侧对齐 */\n    height: 40%;        /* 高度为父元素的40% */\n    background-image: repeating-linear-gradient(\n      0deg,\n      transparent,\n      transparent 8px,\n      rgba(255, 255, 255, 0.1) 8px,\n      rgba(255, 255, 255, 0.1) 16px\n    );                  /* 代码行条纹效果 */\n  }\n  \n  .coding-icon-container {\n    position: relative; /* 相对定位 */\n    z-index: 2;         /* 确保在条纹上层 */\n    display: flex;      /* 弹性布局 */\n    flex-direction: column; /* 垂直排列 */\n    align-items: center;    /* 水平居中 */\n    \n    .anticon {\n      font-size: 28px;     /* 图标大小 */\n      color: white;        /* 图标颜色 */\n      margin-bottom: 12px; /* 底部间距 */\n      filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2)); /* 图标阴影 */\n    }\n    \n    .coding-elements {\n      display: flex;       /* 弹性布局 */\n      flex-direction: column; /* 垂直排列 */\n      align-items: center;    /* 水平居中 */\n      \n      .code-element {\n        font-size: 14px;   /* 代码文字大小 */\n        color: rgba(255, 255, 255, 0.8); /* 代码文字颜色 */\n        margin: 2px 0;     /* 上下间距 */\n        font-family: monospace; /* 等宽字体 */\n        font-weight: bold;  /* 文字加粗 */\n      }\n    }\n  }\n}\n\n/* 为中小学生设计的内容区域 */\n.panel-content {\n  flex: 1;              /* 占据剩余空间 */\n  height: 100%;         /* 高度100% */\n  padding: 15px;        /* 内边距 */\n  padding-bottom: 20px; /* 底部额外内边距 */\n  overflow: hidden;     /* 隐藏溢出内容 */\n  background-color: white; /* 背景色 */\n  display: flex;        /* 弹性布局 */\n  flex-direction: column; /* 垂直排列 */\n}\n\n.panel-header {\n  display: flex;        /* 弹性布局 */\n  justify-content: space-between; /* 两端对齐 */\n  align-items: center;  /* 垂直居中 */\n  margin-bottom: 15px;  /* 底部间距 */\n\n  h3 {\n    margin: 0;          /* 移除默认外边距 */\n    font-size: 16px;    /* 标题大小 */\n    color: #4285F4;     /* 标题颜色 */\n    text-shadow: 0 1px 0 rgba(255,255,255,0.8); /* 文字阴影 */\n    font-weight: bold;  /* 文字加粗 */\n    position: relative; /* 相对定位 */\n    \n    &:after {\n      content: \"\";      /* 必须的内容属性 */\n      position: absolute; /* 绝对定位 */\n      bottom: -4px;     /* 底部偏移 */\n      left: 0;          /* 左侧对齐 */\n      width: 100%;      /* 宽度100% */\n      height: 2px;      /* 高度 */\n      background: linear-gradient(to right, #4285F4, transparent); /* 渐变下划线 */\n    }\n  }\n  \n  .kid-button {\n    border-radius: 12px; /* 圆角 */\n    font-weight: bold;   /* 文字加粗 */\n    box-shadow: 0 2px 5px rgba(0,0,0,0.1); /* 按钮阴影 */\n    border: none;        /* 无边框 */\n  }\n}\n\n/* 图片预览区风格改造 */\n.panel-preview {\n  display: grid;        /* 网格布局 */\n  grid-template-columns: repeat(2, 1fr); /* 两列等宽 */\n  grid-gap: 12px;       /* 网格间距 */\n  flex-grow: 1;         /* 占据剩余空间 */\n  overflow-y: auto;     /* 垂直滚动 */\n  padding-right: 4px;   /* 右侧内边距 */\n  margin-bottom: 5px;   /* 底部外边距 */\n\n  .preview-item {\n    width: 100%;        /* 宽度100% */\n    height: 65px;       /* 固定高度 */\n    overflow: hidden;   /* 隐藏溢出内容 */\n    border-radius: 12px; /* 圆角 */\n    border: 2px solid #e0e0e0; /* 边框 */\n    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1); /* 阴影 */\n    transition: all 0.3s ease; /* 过渡效果 */\n    margin-bottom: 0;   /* 移除底部外边距 */\n    \n    &:hover {\n      transform: scale(1.05); /* 悬停时放大 */\n      border-color: #4285F4; /* 悬停时边框颜色 */\n    }\n    \n    .preview-wrapper {\n      width: 100%;      /* 宽度100% */\n      height: 100%;     /* 高度100% */\n      overflow: hidden; /* 隐藏溢出内容 */\n      border-radius: 10px; /* 内部圆角 */\n      position: relative; /* 相对定位 */\n      \n      &:after {\n        content: \"\";    /* 必须的内容属性 */\n        position: absolute; /* 绝对定位 */\n        top: 0;         /* 顶部对齐 */\n        left: 0;        /* 左侧对齐 */\n        right: 0;       /* 右侧对齐 */\n        bottom: 0;      /* 底部对齐 */\n        background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.1)); /* 渐变遮罩 */\n        pointer-events: none; /* 不影响鼠标事件 */\n      }\n      \n      img, video {\n        width: 100%;    /* 宽度100% */\n        height: 100%;   /* 高度100% */\n        object-fit: cover; /* 覆盖填充 */\n        transition: transform 0.3s ease; /* 过渡效果 */\n      }\n      \n      .video-thumbnail-container {\n        position: relative;\n        width: 100%;\n        height: 100%;\n        \n        .video-thumbnail {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n        }\n        \n        .video-play-icon {\n          position: absolute;\n          top: 50%;\n          left: 50%;\n          transform: translate(-50%, -50%);\n          color: white;\n          font-size: 24px;\n          text-shadow: 0 0 8px rgba(0, 0, 0, 0.8);\n          opacity: 0.9;\n          \n          .anticon {\n            background: rgba(0, 0, 0, 0.5);\n            border-radius: 50%;\n            padding: 5px;\n          }\n        }\n      }\n      \n      &:hover img, &:hover video {\n        transform: scale(1.1); /* 悬停时放大 */\n      }\n    }\n  }\n}\n\n/* 空数据容器样式 */\n.empty-preview-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 80px;\n  flex-grow: 1;\n  \n  :deep(.ant-empty) {\n    font-size: 14px;\n    margin: 0;\n    \n    .ant-empty-image {\n      height: 40px;\n      margin-bottom: 5px;\n    }\n    \n    .ant-empty-description {\n      color: #999;\n    }\n  }\n}\n\n/* 动画效果 */\n@keyframes float {\n  0%, 100% { transform: translateY(-50%); } /* 起始和结束位置 */\n  50% { transform: translateY(-60%); }      /* 中间位置 */\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 1; }   /* 起始和结束透明度 */\n  50% { opacity: 0.8; }      /* 中间透明度 */\n}\n\n@keyframes twinkle {\n  0% { opacity: 0.4; transform: scale(0.8); }  /* 起始状态 */\n  100% { opacity: 1; transform: scale(1.2); }  /* 结束状态 */\n}\n\n/* 进入和离开的动画效果 */\n.slide-enter-active,\n.slide-leave-active {\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* 弹性过渡 */\n}\n\n.slide-enter, .slide-leave-to {\n  transform: translateX(calc(100% - 50px)); /* 从右侧滑入滑出 */\n}\n\n/* 全局样式 - 为了确保弹窗也有少儿编程风格 */\n:global(.kid-friendly-modal .ant-modal-content) {\n  border-radius: 20px;  /* 圆角 */\n  overflow: hidden;     /* 隐藏溢出内容 */\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2); /* 阴影 */\n  border: 3px solid #4285F4; /* 边框 */\n}\n\n:global(.kid-friendly-modal .ant-modal-body) {\n  padding: 0;           /* 移除内边距 */\n}\n", {"version": 3, "sources": ["SlidingPanel.vue"], "names": [], "mappings": ";AA0OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "SlidingPanel.vue", "sourceRoot": "src/views/home/<USER>", "sourcesContent": ["<!-- 触碰型侧边组件 - 悬停时展开并显示照片墙 -->\n<template>\n  <div class=\"sliding-panel-container\">\n    <!-- 横向对话泡泡 - 少儿编程风格 -->\n    <div class=\"tooltip-container\" v-if=\"showBubble && !isExpanded\">\n      <div class=\"tooltip kid-friendly-bubble\" @click.stop=\"hideBubble\">\n        <div class=\"bubble-content\">\n          <div class=\"stars1\">\n            <span class=\"star1\">★</span><br>\n            <span class=\"star1\">★</span>\n          </div>\n          点击浏览小朋友们<br>精彩瞬间～\n          <div class=\"stars2\">\n            <span class=\"star2\">★</span><br>\n            <span class=\"star2\">★</span>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <transition name=\"slide\">\n      <div \n        class=\"sliding-panel kid-friendly-panel\" \n        :class=\"{ 'expanded': isExpanded }\"\n        @mouseenter=\"expandPanel\"\n        @mouseleave=\"collapsePanel\"\n        @click=\"handlePanelClick\"\n      >\n        <div class=\"panel-avatar\">\n          <div class=\"coding-icon-container\">\n            <a-icon type=\"camera\" theme=\"filled\" />\n          </div>\n        </div>\n        <div v-show=\"isExpanded\" class=\"panel-content\">\n          <div class=\"panel-header\">\n            <h3>小小创客照片墙</h3>\n            <a-button type=\"primary\" size=\"small\" class=\"kid-button\" @click.stop=\"openMediaWall\">查看全部</a-button>\n          </div>\n          <div v-if=\"previewItems.length > 0\" class=\"panel-preview\">\n            <div v-for=\"(item, index) in previewItems\" :key=\"index\" class=\"preview-item\">\n              <div class=\"preview-wrapper\">\n                <img v-if=\"item.type === 'image'\" :src=\"getFileUrl(item.url)\" :alt=\"`预览图片${index+1}`\" />\n                <div v-if=\"item.type === 'video'\" class=\"video-thumbnail-container\">\n                  <img :src=\"getFileUrl(item.url)\" :alt=\"`视频封面${index+1}`\" class=\"video-thumbnail\" />\n                  <div class=\"video-play-icon\">\n                    <a-icon type=\"play-circle\" />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div v-else class=\"empty-preview-container\">\n              <a-empty description=\"暂无照片\" />\n          </div>\n        </div>\n      </div>\n    </transition>\n\n    <!-- 照片墙弹窗 -->\n    <a-modal\n      v-model=\"mediaWallVisible\"\n      :width=\"modalWidth\"\n      :footer=\"null\"\n      :maskClosable=\"true\"\n      destroyOnClose\n      @cancel=\"closeMediaWall\"\n      wrapClassName=\"kid-friendly-modal\"\n    >\n      <media-wall\n        :media-items=\"mediaItems\"\n        @close=\"closeMediaWall\"\n      />\n    </a-modal>\n  </div>\n</template>\n\n<script>\nimport MediaWall from './MediaWall.vue'\nimport { getAction, getFileAccessHttpUrl } from '@/api/manage'\n\nexport default {\n  name: 'SlidingPanel',\n  components: {\n    MediaWall\n  },\n  data() {\n    return {\n      isExpanded: false,\n      mediaWallVisible: false,\n      modalWidth: '77vw',\n      showBubble: true,\n      shortTimerId: null,\n      longTimerId: null,\n      hasViewedMediaWall: false,\n      // 预览图片，在侧边栏中显示\n      previewItems: [],\n      // 媒体墙中显示的所有项目\n      mediaItems: [],\n      // 系统配置\n      sysConfig: {}\n    }\n  },\n  mounted() {\n    // 初始化时显示泡泡\n    this.showBubble = true;\n    // 加载系统配置\n    this.sysConfig = this.$store.getters.sysConfig;\n    // 从服务器加载照片墙数据\n    this.loadMediaData();\n  },\n  beforeDestroy() {\n    // 组件销毁前清理定时器\n    this.clearTimers();\n  },\n  methods: {\n    // 从服务器加载照片墙数据\n    loadMediaData() {\n      // 按照Banner组件的方式处理数据\n      if (this.sysConfig.photoWallMedia_urls) {\n        // 使用新格式\n        const urls = this.sysConfig.photoWallMedia_urls.split(',');\n        const thumbnails = this.sysConfig.photoWallMedia_thumbnails ? this.sysConfig.photoWallMedia_thumbnails.split(',') : [];\n        const types = this.sysConfig.photoWallMedia_types ? this.sysConfig.photoWallMedia_types.split(',') : [];\n        const titles = this.sysConfig.photoWallMedia_titles ? this.sysConfig.photoWallMedia_titles.split(',') : [];\n        \n        this.mediaItems = [];\n        \n        for (let i = 0; i < urls.length; i++) {\n          // 保持原始路径，由getFileUrl函数处理URL转换\n          let item = {\n            id: i + 1,\n            type: types[i] || 'image',\n            title: titles[i] || `媒体${i + 1}`,\n            url: urls[i],\n            thumbnail: thumbnails[i] || urls[i]\n          };\n          this.mediaItems.push(item);\n        }\n        \n        // 使用前4个项目作为预览\n        this.previewItems = this.mediaItems.slice(0, 4).map(item => ({\n          type: item.type,\n          url: item.thumbnail || item.url\n        }));\n      } else if (this.sysConfig.photoWallMedia) {\n        // 使用旧格式\n        try {\n          let items = JSON.parse(this.sysConfig.photoWallMedia || '[]');\n          this.mediaItems = items.map(item => ({\n            ...item,\n            // 保持原始路径，由getFileUrl函数处理URL转换\n            url: item.url,\n            thumbnail: item.thumbnail || item.url\n          }));\n          \n          // 使用前4个项目作为预览\n          this.previewItems = this.mediaItems.slice(0, 4).map(item => ({\n            type: item.type,\n            url: item.thumbnail || item.url\n          }));\n        } catch (e) {\n          this.mediaItems = [];\n          this.previewItems = [];\n        }\n      } else {\n        this.mediaItems = [];\n        this.previewItems = [];\n      }\n    },\n    \n    expandPanel() {\n      this.isExpanded = true;\n      this.hideBubble();\n    },\n    collapsePanel() {\n      if (!this.mediaWallVisible) {\n        this.isExpanded = false;\n      }\n    },\n    openMediaWall() {\n      this.mediaWallVisible = true;\n      this.hasViewedMediaWall = true;\n      this.hideBubble();\n    },\n    closeMediaWall() {\n      this.mediaWallVisible = false;\n      this.startLongTimer();\n    },\n    hideBubble() {\n      this.showBubble = false;\n      this.startShortTimer();\n    },\n    handlePanelClick() {\n      this.hideBubble();\n    },\n    startShortTimer() {\n      if (this.shortTimerId) {\n        clearTimeout(this.shortTimerId);\n      }\n      \n      this.shortTimerId = setTimeout(() => {\n        if (!this.isExpanded && !this.mediaWallVisible) {\n          this.showBubble = true;\n        }\n      }, 30000);\n    },\n    startLongTimer() {\n      if (this.longTimerId) {\n        clearTimeout(this.longTimerId);\n      }\n      \n      this.longTimerId = setTimeout(() => {\n        if (this.hasViewedMediaWall && !this.isExpanded && !this.mediaWallVisible) {\n          this.showBubble = true;\n        }\n      }, 60000);\n    },\n    clearTimers() {\n      if (this.shortTimerId) {\n        clearTimeout(this.shortTimerId);\n      }\n      if (this.longTimerId) {\n        clearTimeout(this.longTimerId);\n      }\n    },\n    // 添加URL处理方法\n    getFileUrl(path) {\n      return getFileAccessHttpUrl(path);\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n/* 定义儿童友好的色彩主题 */\n:root {\n  --primary-color: #4285F4;\n  --secondary-color: #34A853;\n  --accent-color: #FBBC05;\n  --accent2-color: #EA4335;\n  --light-color: #E8F0FE;\n  --dark-color: #1A73E8;\n}\n\n/* 滑动面板容器 - 固定在屏幕右侧，垂直居中偏上位置 */\n.sliding-panel-container {\n  position: fixed;  /* 固定定位，不随页面滚动 */\n  right: 0;         /* 贴靠屏幕右侧 */\n  top: 80%;      /* 位于屏幕80%高度处，更靠下 */\n  transform: translateY(-50%); /* 垂直居中调整 */\n  z-index: 999;     /* 确保在其他元素上层显示 */\n}\n\n/* 对话泡泡容器 - 少儿编程风格 */\n.tooltip-container {\n  position: absolute; /* 绝对定位，相对于父元素 */\n  top: 0;            /* 顶部对齐 */\n  right: 55px;       /* 距离右侧55像素 */\n  z-index: 1001;     /* 确保在面板上层显示 */\n}\n\n/* 少儿编程风格的对话泡泡 */\n.kid-friendly-bubble {\n  position: relative;   /* 相对定位，便于添加伪元素 */\n  display: inline-block; /* 行内块元素 */\n  padding: 12px 20px;    /* 内边距 */\n  background: linear-gradient(135deg, #42b883, #35495e); /* 渐变背景 */\n  border-radius: 18px;   /* 圆角边框 */\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 0 6px rgba(66, 184, 131, 0.4); /* 双层阴影效果 */\n  color: white;          /* 文字颜色 */\n  font-size: 15px;       /* 文字大小 */\n  width: auto;           /* 自适应宽度 */\n  white-space: nowrap;   /* 文本不换行 */\n  text-align: center;    /* 文本居中 */\n  cursor: pointer;       /* 鼠标指针样式 */\n  transform: translateY(-50%); /* 垂直居中调整 */\n  animation: float 3s ease-in-out infinite; /* 浮动动画 */\n  border: 2px solid white; /* 白色边框 */\n  font-weight: bold;     /* 文字加粗 */\n  \n  .bubble-content {\n    position: relative; /* 相对定位 */\n  }\n\n  .stars1 {\n    position: absolute; /* 绝对定位 */\n    top: 2px;         /* 顶部偏移 */\n    right: 122px;       /* 右侧偏移 */\n    font-size: 14px;    /* 星星大小 */\n    color: #FBBC05;     /* 星星颜色 */\n    animation: twinkle 1.5s ease-in-out infinite alternate; /* 闪烁动画 */\n    \n    .star1:nth-child(2) {\n      animation-delay: 0.7s; /* 第二个星星动画延迟 */\n      margin-left: 5px;      /* 左侧间距 */\n    }\n  }\n  \n  .stars2 {\n    position: absolute; /* 绝对定位 */\n    top: 2px;         /* 顶部偏移 */\n    right: -17px;       /* 右侧偏移 */\n    font-size: 14px;    /* 星星大小 */\n    color: #FBBC05;     /* 星星颜色 */\n    animation: twinkle 1.5s ease-in-out infinite alternate; /* 闪烁动画 */\n    \n    .star2:nth-child(2) {\n      animation-delay: 0.7s; /* 第二个星星动画延迟 */\n      margin-left: 5px;      /* 左侧间距 */\n    }\n  }\n}\n\n/* 三角形指向 - 动画版 */\n.kid-friendly-bubble:after {\n  content: '';          /* 必须的内容属性 */\n  position: absolute;   /* 绝对定位 */\n  top: 50%;             /* 垂直居中 */\n  right: -12px;         /* 右侧偏移 */\n  margin-top: -10px;    /* 微调垂直位置 */\n  border-width: 10px 0 10px 12px; /* 三角形边框宽度 */\n  border-style: solid;  /* 边框样式 */\n  border-color: transparent transparent transparent #35495e; /* 只显示左边框形成三角形 */\n  animation: pulse 2s infinite; /* 脉冲动画 */\n}\n\n/* 面向中小学生的侧边面板 */\n.kid-friendly-panel {\n  display: flex;        /* 弹性布局 */\n  align-items: center;  /* 垂直居中 */\n  background: linear-gradient(to bottom, #ffffff, #f0f8ff); /* 渐变背景 */\n  border-radius: 20px 0 0 20px; /* 左侧圆角 */\n  box-shadow: -4px 4px 15px rgba(0, 0, 0, 0.15); /* 阴影效果 */\n  overflow: hidden;     /* 隐藏溢出内容 */\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* 平滑过渡效果 */\n  height: 180px;        /* 固定高度 */\n  cursor: pointer;      /* 鼠标指针样式 */\n  border: 2px solid #e0e0e0; /* 边框 */\n  \n  &.expanded {\n    width: 320px;       /* 展开时的宽度 */\n    box-shadow: -6px 6px 20px rgba(0, 0, 0, 0.2); /* 展开时更强的阴影 */\n  }\n  \n  &:hover {\n    transform: translateX(-5px); /* 悬停时向左移动 */\n  }\n}\n\n/* 程序员风格的图标区域 */\n.panel-avatar {\n  width: 50px;          /* 固定宽度 */\n  height: 180px;        /* 与面板同高 */\n  display: flex;        /* 弹性布局 */\n  align-items: center;  /* 垂直居中 */\n  justify-content: center; /* 水平居中 */\n  background: linear-gradient(135deg, #4285F4, #0F9D58); /* 谷歌风格渐变 */\n  position: relative;   /* 相对定位 */\n  overflow: hidden;     /* 隐藏溢出内容 */\n  \n  &:before {\n    content: \"\";        /* 必须的内容属性 */\n    position: absolute; /* 绝对定位 */\n    top: 0;             /* 顶部对齐 */\n    left: 0;            /* 左侧对齐 */\n    right: 0;           /* 右侧对齐 */\n    height: 40%;        /* 高度为父元素的40% */\n    background-image: repeating-linear-gradient(\n      0deg,\n      transparent,\n      transparent 8px,\n      rgba(255, 255, 255, 0.1) 8px,\n      rgba(255, 255, 255, 0.1) 16px\n    );                  /* 代码行条纹效果 */\n  }\n  \n  .coding-icon-container {\n    position: relative; /* 相对定位 */\n    z-index: 2;         /* 确保在条纹上层 */\n    display: flex;      /* 弹性布局 */\n    flex-direction: column; /* 垂直排列 */\n    align-items: center;    /* 水平居中 */\n    \n    .anticon {\n      font-size: 28px;     /* 图标大小 */\n      color: white;        /* 图标颜色 */\n      margin-bottom: 12px; /* 底部间距 */\n      filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2)); /* 图标阴影 */\n    }\n    \n    .coding-elements {\n      display: flex;       /* 弹性布局 */\n      flex-direction: column; /* 垂直排列 */\n      align-items: center;    /* 水平居中 */\n      \n      .code-element {\n        font-size: 14px;   /* 代码文字大小 */\n        color: rgba(255, 255, 255, 0.8); /* 代码文字颜色 */\n        margin: 2px 0;     /* 上下间距 */\n        font-family: monospace; /* 等宽字体 */\n        font-weight: bold;  /* 文字加粗 */\n      }\n    }\n  }\n}\n\n/* 为中小学生设计的内容区域 */\n.panel-content {\n  flex: 1;              /* 占据剩余空间 */\n  height: 100%;         /* 高度100% */\n  padding: 15px;        /* 内边距 */\n  padding-bottom: 20px; /* 底部额外内边距 */\n  overflow: hidden;     /* 隐藏溢出内容 */\n  background-color: white; /* 背景色 */\n  display: flex;        /* 弹性布局 */\n  flex-direction: column; /* 垂直排列 */\n}\n\n.panel-header {\n  display: flex;        /* 弹性布局 */\n  justify-content: space-between; /* 两端对齐 */\n  align-items: center;  /* 垂直居中 */\n  margin-bottom: 15px;  /* 底部间距 */\n\n  h3 {\n    margin: 0;          /* 移除默认外边距 */\n    font-size: 16px;    /* 标题大小 */\n    color: #4285F4;     /* 标题颜色 */\n    text-shadow: 0 1px 0 rgba(255,255,255,0.8); /* 文字阴影 */\n    font-weight: bold;  /* 文字加粗 */\n    position: relative; /* 相对定位 */\n    \n    &:after {\n      content: \"\";      /* 必须的内容属性 */\n      position: absolute; /* 绝对定位 */\n      bottom: -4px;     /* 底部偏移 */\n      left: 0;          /* 左侧对齐 */\n      width: 100%;      /* 宽度100% */\n      height: 2px;      /* 高度 */\n      background: linear-gradient(to right, #4285F4, transparent); /* 渐变下划线 */\n    }\n  }\n  \n  .kid-button {\n    border-radius: 12px; /* 圆角 */\n    font-weight: bold;   /* 文字加粗 */\n    box-shadow: 0 2px 5px rgba(0,0,0,0.1); /* 按钮阴影 */\n    border: none;        /* 无边框 */\n  }\n}\n\n/* 图片预览区风格改造 */\n.panel-preview {\n  display: grid;        /* 网格布局 */\n  grid-template-columns: repeat(2, 1fr); /* 两列等宽 */\n  grid-gap: 12px;       /* 网格间距 */\n  flex-grow: 1;         /* 占据剩余空间 */\n  overflow-y: auto;     /* 垂直滚动 */\n  padding-right: 4px;   /* 右侧内边距 */\n  margin-bottom: 5px;   /* 底部外边距 */\n\n  .preview-item {\n    width: 100%;        /* 宽度100% */\n    height: 65px;       /* 固定高度 */\n    overflow: hidden;   /* 隐藏溢出内容 */\n    border-radius: 12px; /* 圆角 */\n    border: 2px solid #e0e0e0; /* 边框 */\n    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1); /* 阴影 */\n    transition: all 0.3s ease; /* 过渡效果 */\n    margin-bottom: 0;   /* 移除底部外边距 */\n    \n    &:hover {\n      transform: scale(1.05); /* 悬停时放大 */\n      border-color: #4285F4; /* 悬停时边框颜色 */\n    }\n    \n    .preview-wrapper {\n      width: 100%;      /* 宽度100% */\n      height: 100%;     /* 高度100% */\n      overflow: hidden; /* 隐藏溢出内容 */\n      border-radius: 10px; /* 内部圆角 */\n      position: relative; /* 相对定位 */\n      \n      &:after {\n        content: \"\";    /* 必须的内容属性 */\n        position: absolute; /* 绝对定位 */\n        top: 0;         /* 顶部对齐 */\n        left: 0;        /* 左侧对齐 */\n        right: 0;       /* 右侧对齐 */\n        bottom: 0;      /* 底部对齐 */\n        background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.1)); /* 渐变遮罩 */\n        pointer-events: none; /* 不影响鼠标事件 */\n      }\n      \n      img, video {\n        width: 100%;    /* 宽度100% */\n        height: 100%;   /* 高度100% */\n        object-fit: cover; /* 覆盖填充 */\n        transition: transform 0.3s ease; /* 过渡效果 */\n      }\n      \n      .video-thumbnail-container {\n        position: relative;\n        width: 100%;\n        height: 100%;\n        \n        .video-thumbnail {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n        }\n        \n        .video-play-icon {\n          position: absolute;\n          top: 50%;\n          left: 50%;\n          transform: translate(-50%, -50%);\n          color: white;\n          font-size: 24px;\n          text-shadow: 0 0 8px rgba(0, 0, 0, 0.8);\n          opacity: 0.9;\n          \n          .anticon {\n            background: rgba(0, 0, 0, 0.5);\n            border-radius: 50%;\n            padding: 5px;\n          }\n        }\n      }\n      \n      &:hover img, &:hover video {\n        transform: scale(1.1); /* 悬停时放大 */\n      }\n    }\n  }\n}\n\n/* 空数据容器样式 */\n.empty-preview-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 80px;\n  flex-grow: 1;\n  \n  :deep(.ant-empty) {\n    font-size: 14px;\n    margin: 0;\n    \n    .ant-empty-image {\n      height: 40px;\n      margin-bottom: 5px;\n    }\n    \n    .ant-empty-description {\n      color: #999;\n    }\n  }\n}\n\n/* 动画效果 */\n@keyframes float {\n  0%, 100% { transform: translateY(-50%); } /* 起始和结束位置 */\n  50% { transform: translateY(-60%); }      /* 中间位置 */\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 1; }   /* 起始和结束透明度 */\n  50% { opacity: 0.8; }      /* 中间透明度 */\n}\n\n@keyframes twinkle {\n  0% { opacity: 0.4; transform: scale(0.8); }  /* 起始状态 */\n  100% { opacity: 1; transform: scale(1.2); }  /* 结束状态 */\n}\n\n/* 进入和离开的动画效果 */\n.slide-enter-active,\n.slide-leave-active {\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* 弹性过渡 */\n}\n\n.slide-enter, .slide-leave-to {\n  transform: translateX(calc(100% - 50px)); /* 从右侧滑入滑出 */\n}\n\n/* 全局样式 - 为了确保弹窗也有少儿编程风格 */\n:global(.kid-friendly-modal .ant-modal-content) {\n  border-radius: 20px;  /* 圆角 */\n  overflow: hidden;     /* 隐藏溢出内容 */\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2); /* 阴影 */\n  border: 3px solid #4285F4; /* 边框 */\n}\n\n:global(.kid-friendly-modal .ant-modal-body) {\n  padding: 0;           /* 移除内边距 */\n}\n</style> "]}]}