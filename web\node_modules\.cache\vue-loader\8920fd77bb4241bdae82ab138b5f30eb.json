{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\Index.vue?vue&type=style&index=0&id=d2d75d0c&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\Index.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n.account-settings-info-main {\n  width: 100%;\n  display: flex;\n  height: 100%;\n  overflow: auto;\n\n  &.mobile {\n    display: block;\n\n    .account-settings-info-left {\n      border-right: unset;\n      border-bottom: 1px solid #e8e8e8;\n      width: 100%;\n      height: 50px;\n      overflow-x: auto;\n      overflow-y: scroll;\n    }\n    .account-settings-info-right {\n      padding: 20px 40px;\n    }\n  }\n\n  .account-settings-info-left {\n    border-right: 1px solid #e8e8e8;\n    width: 224px;\n  }\n\n  .account-settings-info-right {\n    flex: 1 1;\n    padding: 8px 40px;\n    .account-settings-info-view {\n      padding-top: 12px;\n    }\n  }\n}\n\n", {"version": 3, "sources": ["Index.vue"], "names": [], "mappings": ";AAyIA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Index.vue", "sourceRoot": "src/views/account/course", "sourcesContent": ["<template>\n  <div class=\"page-header-index-wide\">\n    <a-card :bordered=\"false\" :bodyStyle=\"{ padding: '16px 0', height: '100%' }\" :style=\"{ height: '100%' }\">\n      <div class=\"account-settings-info-main\" :class=\"device\" :style=\" 'min-height:'+ mainInfoHeight \">\n        <div class=\"account-settings-info-left\">\n          <a-menu\n            :mode=\"device == 'mobile' ? 'horizontal' : 'inline'\"\n            :style=\"{ border: '0', width: device == 'mobile' ? '560px' : 'auto'}\"\n            :defaultSelectedKeys=\"defaultSelectedKeys\"\n            type=\"inner\"\n            @openChange=\"onOpenChange\"\n          >\n            <a-menu-item v-for=\"(course,index) in courseList\" :key=\"'/teaching/mineCourse/course?id='+course.id\">\n              <router-link :to=\"{ name: 'teaching-mineCourse-course',  query: {id:course.id}}\" :meta=\"{keepAlive:false}\">\n                {{course.courseName}}\n              </router-link>\n            </a-menu-item>\n          </a-menu>\n        </div>\n        <div class=\"account-settings-info-right\">\n          <route-view></route-view>\n        </div>\n      </div>\n    </a-card>\n  </div>\n</template>\n\n<script>\n  import PageLayout from '@/components/page/PageLayout'\n  import RouteView from \"@/components/layouts/RouteView\"\n  import { getAction } from '@/api/manage'\n  import { mixinDevice } from '@/utils/mixin.js'\n\n  export default {\n    components: {\n      RouteView,\n      PageLayout\n    },\n    mixins: [mixinDevice],\n    data () {\n      return {\n        // horizontal  inline\n        mode: 'inline',\n        mainInfoHeight:\"100%\",\n        openKeys: [],\n        defaultSelectedKeys: [],\n\n        // cropper\n        preview: {},\n        option: {\n          img: '/avatar2.jpg',\n          info: true,\n          size: 1,\n          outputType: 'jpeg',\n          canScale: false,\n          autoCrop: true,\n          // 只有自动截图开启 宽度高度才生效\n          autoCropWidth: 180,\n          autoCropHeight: 180,\n          fixedBox: true,\n          // 开启宽度和高度比例\n          fixed: true,\n          fixedNumber: [1, 1]\n        },\n        courseList: [],\n        url: {\n          mineCourseList: '/teaching/teachingCourse/mineCourse'\n        },\n        pageTitle: ''\n      }\n    },\n    created () {\n      this.initMineCourse()\n      this.updateMenu()\n    },\n    mounted(){\n      this.mainInfoHeight = (window.innerHeight-285)+\"px\";\n    },\n    methods: {\n      onOpenChange (openKeys) {\n        this.openKeys = openKeys\n      },\n      updateMenu () {\n        let routes = this.$route.matched.concat()\n        this.defaultSelectedKeys = [ routes.pop().path ]\n      },\n      initMineCourse(){\n        getAction(this.url.mineCourseList, {}).then(res => {\n          if(res.success){\n            this.courseList = res.result\n          }else{\n            console.log(res)\n          }\n        })\n      }\n    },\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .account-settings-info-main {\n    width: 100%;\n    display: flex;\n    height: 100%;\n    overflow: auto;\n\n    &.mobile {\n      display: block;\n\n      .account-settings-info-left {\n        border-right: unset;\n        border-bottom: 1px solid #e8e8e8;\n        width: 100%;\n        height: 50px;\n        overflow-x: auto;\n        overflow-y: scroll;\n      }\n      .account-settings-info-right {\n        padding: 20px 40px;\n      }\n    }\n\n    .account-settings-info-left {\n      border-right: 1px solid #e8e8e8;\n      width: 224px;\n    }\n\n    .account-settings-info-right {\n      flex: 1 1;\n      padding: 8px 40px;\n      .account-settings-info-view {\n        padding-top: 12px;\n      }\n    }\n  }\n\n</style>"]}]}