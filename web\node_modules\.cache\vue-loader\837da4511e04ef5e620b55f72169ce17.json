{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitListCard.vue?vue&type=template&id=1cca1d35&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseUnitListCard.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"app-list\"\n  }, [_vm._l(_vm.dataSource, function (item) {\n    return _c(\"a-card\", {\n      key: item.id,\n      on: {\n        click: function click($event) {\n          return _vm.viewUnit(item);\n        }\n      }\n    }, [_c(\"a-card-meta\", [_c(\"div\", {\n      staticStyle: {\n        \"margin-bottom\": \"3px\"\n      },\n      attrs: {\n        slot: \"title\"\n      },\n      slot: \"title\"\n    }, [_c(\"a-icon\", {\n      attrs: {\n        type: \"right-circle\"\n      }\n    }), _vm._v(\"  \\n          \" + _vm._s(item.unitName) + \"\\n        \")], 1), _c(\"div\", {\n      staticClass: \"meta-cardInfo\",\n      attrs: {\n        slot: \"description\"\n      },\n      slot: \"description\"\n    }, [_c(\"img\", {\n      staticStyle: {\n        width: \"100%\",\n        height: \"100%\"\n      },\n      attrs: {\n        src: _vm.getFileAccessHttpUrl(item.unitCover),\n        height: \"25px\"\n      }\n    })])])], 1);\n  }), _c(\"unitView-modal\", {\n    ref: \"unitViewModal\"\n  })], 2);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_l", "dataSource", "item", "key", "id", "on", "click", "$event", "viewUnit", "staticStyle", "attrs", "slot", "type", "_v", "_s", "unitName", "width", "height", "src", "getFileAccessHttpUrl", "unitCover", "ref", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/account/course/CourseUnitListCard.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"app-list\" },\n    [\n      _vm._l(_vm.dataSource, function (item) {\n        return _c(\n          \"a-card\",\n          {\n            key: item.id,\n            on: {\n              click: function ($event) {\n                return _vm.viewUnit(item)\n              },\n            },\n          },\n          [\n            _c(\"a-card-meta\", [\n              _c(\n                \"div\",\n                {\n                  staticStyle: { \"margin-bottom\": \"3px\" },\n                  attrs: { slot: \"title\" },\n                  slot: \"title\",\n                },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"right-circle\" } }),\n                  _vm._v(\n                    \"  \\n          \" + _vm._s(item.unitName) + \"\\n        \"\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"meta-cardInfo\",\n                  attrs: { slot: \"description\" },\n                  slot: \"description\",\n                },\n                [\n                  _c(\"img\", {\n                    staticStyle: { width: \"100%\", height: \"100%\" },\n                    attrs: {\n                      src: _vm.getFileAccessHttpUrl(item.unitCover),\n                      height: \"25px\",\n                    },\n                  }),\n                ]\n              ),\n            ]),\n          ],\n          1\n        )\n      }),\n      _c(\"unitView-modal\", { ref: \"unitViewModal\" }),\n    ],\n    2\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAW,CAAC,EAC3B,CACEH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,UAAU,EAAE,UAAUC,IAAI,EAAE;IACrC,OAAOL,EAAE,CACP,QAAQ,EACR;MACEM,GAAG,EAAED,IAAI,CAACE,EAAE;MACZC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;UACvB,OAAOX,GAAG,CAACY,QAAQ,CAACN,IAAI,CAAC;QAC3B;MACF;IACF,CAAC,EACD,CACEL,EAAE,CAAC,aAAa,EAAE,CAChBA,EAAE,CACA,KAAK,EACL;MACEY,WAAW,EAAE;QAAE,eAAe,EAAE;MAAM,CAAC;MACvCC,KAAK,EAAE;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACxBA,IAAI,EAAE;IACR,CAAC,EACD,CACEd,EAAE,CAAC,QAAQ,EAAE;MAAEa,KAAK,EAAE;QAAEE,IAAI,EAAE;MAAe;IAAE,CAAC,CAAC,EACjDhB,GAAG,CAACiB,EAAE,CACJ,gBAAgB,GAAGjB,GAAG,CAACkB,EAAE,CAACZ,IAAI,CAACa,QAAQ,CAAC,GAAG,YAC7C,CAAC,CACF,EACD,CACF,CAAC,EACDlB,EAAE,CACA,KAAK,EACL;MACEE,WAAW,EAAE,eAAe;MAC5BW,KAAK,EAAE;QAAEC,IAAI,EAAE;MAAc,CAAC;MAC9BA,IAAI,EAAE;IACR,CAAC,EACD,CACEd,EAAE,CAAC,KAAK,EAAE;MACRY,WAAW,EAAE;QAAEO,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO,CAAC;MAC9CP,KAAK,EAAE;QACLQ,GAAG,EAAEtB,GAAG,CAACuB,oBAAoB,CAACjB,IAAI,CAACkB,SAAS,CAAC;QAC7CH,MAAM,EAAE;MACV;IACF,CAAC,CAAC,CAEN,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC;EACH,CAAC,CAAC,EACFpB,EAAE,CAAC,gBAAgB,EAAE;IAAEwB,GAAG,EAAE;EAAgB,CAAC,CAAC,CAC/C,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB3B,MAAM,CAAC4B,aAAa,GAAG,IAAI;AAE3B,SAAS5B,MAAM,EAAE2B,eAAe", "ignoreList": []}]}