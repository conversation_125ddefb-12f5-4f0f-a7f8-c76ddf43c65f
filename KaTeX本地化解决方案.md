# KaTeX本地化解决方案

## 问题描述
原项目中的KaTeX数学公式渲染库通过CDN动态加载，导致网络问题时加载失败，影响数学公式的正常显示。

## 解决方案

### 1. 安装KaTeX到本地
```bash
cd web
npm install katex@0.16.8 --save --legacy-peer-deps
```

### 2. 修改mathRenderer.js文件
将CDN加载方式改为ES6模块导入方式：

**原代码（CDN方式）：**
```javascript
// 动态加载KaTeX CSS
const cssLink = document.createElement('link')
cssLink.rel = 'stylesheet'
cssLink.href = 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css'
document.head.appendChild(cssLink)

// 动态加载KaTeX JS
const script = document.createElement('script')
script.src = 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js'
```

**修改后代码（本地导入）：**
```javascript
// 导入KaTeX库和样式
import katex from 'katex'
import 'katex/dist/katex.min.css'

// 初始化方法简化
async initKaTeX() {
  try {
    // 直接使用导入的KaTeX库
    if (katex) {
      // 将KaTeX挂载到window对象上，保持兼容性
      window.katex = katex
      this.katexLoaded = true
      console.log('KaTeX初始化完成')
      this.processQueue()
      return
    }
    console.error('KaTeX库未正确导入')
  } catch (error) {
    console.error('KaTeX初始化失败:', error)
  }
}
```

### 3. 优势对比

| 方案 | CDN加载 | 本地导入 |
|------|---------|----------|
| 网络依赖 | 需要网络连接 | 无需网络 |
| 加载速度 | 受网络影响 | 本地加载快 |
| 稳定性 | 可能失败 | 稳定可靠 |
| 缓存 | 浏览器缓存 | 打包到bundle |
| 版本控制 | 依赖CDN | 锁定版本 |

### 4. 测试验证
创建了测试页面 `/math-test` 来验证KaTeX功能：
- 行内公式渲染
- 块级公式渲染
- 复杂数学公式
- 动态公式输入
- 公式模板展示

### 5. 使用方法
在Vue组件中使用数学公式：

```vue
<template>
  <!-- 行内公式 -->
  <span v-math="'E = mc^2'"></span>
  
  <!-- 块级公式 -->
  <div v-math.display="'\\sum_{i=1}^{n} x_i'"></div>
</template>

<script>
import { mathDirective } from '@/utils/mathRenderer'

export default {
  directives: {
    math: mathDirective
  }
}
</script>
```

### 6. 注意事项
1. 使用 `--legacy-peer-deps` 参数解决依赖冲突
2. 保持window.katex的兼容性，确保现有代码正常工作
3. CSS样式自动导入，无需手动引入
4. 支持所有原有的KaTeX功能和配置选项

## 总结
通过本地化KaTeX库，彻底解决了网络加载问题，提高了数学公式渲染的稳定性和性能。这是一个更简洁、更可靠的解决方案。
