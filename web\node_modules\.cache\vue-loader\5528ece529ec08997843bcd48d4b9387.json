{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\MineWorkList.vue?vue&type=style&index=0&id=********&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\MineWorkList.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n@import '~@assets/less/common.less';\n", {"version": 3, "sources": ["MineWorkList.vue"], "names": [], "mappings": ";AA6UA", "file": "MineWorkList.vue", "sourceRoot": "src/views/account/center", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <!-- 查询区域 -->\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\">\n        <a-row :gutter=\"24\">\n          <a-col :md=\"6\" :sm=\"8\">\n            <a-form-item label=\"作品名\">\n              <a-input placeholder=\"请输入作品名\" v-model=\"queryParam.workName\"></a-input>\n            </a-form-item>\n          </a-col>\n          <a-col :xl=\"4\" :lg=\"5\" :md=\"7\" :sm=\"24\">\n            <a-form-item label=\"标签\">\n              <a-select v-model=\"queryParam['workTag']\" showSearch>\n                <a-select-option v-for=\"(t,i) in workTag\" :key=\"i\" :value=\"t\">{{t}}</a-select-option>\n              </a-select>\n            </a-form-item>\n          </a-col>\n          <a-col :xl=\"4\" :lg=\"5\" :md=\"7\" :sm=\"24\">\n            <a-form-item label=\"类型\">\n              <j-dict-select-tag placeholder=\"请选择类型\" v-model=\"queryParam.workType\" dictCode=\"work_type\" />\n            </a-form-item>\n          </a-col>\n          <a-col :md=\"6\" :sm=\"8\">\n            <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n              <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n              <a-button\n                type=\"primary\"\n                @click=\"searchReset\"\n                icon=\"reload\"\n                style=\"margin-left: 8px\"\n              >重置</a-button>\n            </span>\n          </a-col>\n        </a-row>\n      </a-form>\n    </div>\n\n    <!-- 操作按钮区域 -->\n    <div class=\"table-operator\">\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\" @click=\"batchDel\">\n            <a-icon type=\"delete\" />删除\n          </a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\">\n          批量操作\n          <a-icon type=\"down\" />\n        </a-button>\n      </a-dropdown>\n    </div>\n\n    <!-- table区域-begin -->\n    <div>\n      <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n        <i class=\"anticon anticon-info-circle ant-alert-icon\"></i> 已选择\n        <a style=\"font-weight: 600\">{{ selectedRowKeys.length }}</a>项\n        <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n      </div>\n\n      <a-table\n        ref=\"table\"\n        size=\"middle\"\n        bordered\n        rowKey=\"id\"\n        :columns=\"columns\"\n        :dataSource=\"dataSource\"\n        :pagination=\"ipagination\"\n        :loading=\"loading\"\n        :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n        @change=\"handleTableChange\"\n      >\n        <template slot=\"imgSlot\" slot-scope=\"text\">\n          <span v-if=\"!text\" style=\"font-size: 12px;font-style: italic;\">无此图片</span>\n          <img\n            v-else\n            :src=\"text\"\n            height=\"25px\"\n            alt=\"图片不存在\"\n            style=\"max-width:80px;font-size: 12px;font-style: italic;\"\n          />\n        </template>\n\n        <a-popover slot=\"workTag\"\n          slot-scope=\"text, row\"  title=\"作品标签\" trigger=\"click\">\n          <div slot=\"content\">\n            <div v-if=\"workTag.length>0\">\n              <span>快捷选择：</span>\n              <a-tag v-for=\"(t,i) in workTag\" :key=\"i\" @click=\"workTagValue=t\" closable @close=\"delWorkTag($event,t)\">{{t}}</a-tag>\n              <a-divider></a-divider>\n            </div>\n            <a-input :value=\"workTagValue\" @change=\"v=>workTagValue=v.target.value\" style=\"width:200px;\"></a-input>\n            <a-button type=\"primary\" @click=\"setWorkTag(row.id)\">添加</a-button>\n          </div>\n          <a href=\"#\">{{text || '暂无'}}</a>\n        </a-popover>\n\n        <a-tooltip slot=\"scoreInfo\" slot-scope=\"text, row\" :title=\"row.teacherComment\">\n          <a-rate v-model=\"row.score\" disabled />\n        </a-tooltip>\n        \n\n\n        <span slot=\"action\" slot-scope=\"text, record\">\n          <a @click=\"handleView(record)\">查看</a>\n          <a-divider type=\"vertical\"/>\n          <a @click=\"handlePreview(record)\">预览</a>\n          <a-divider type=\"vertical\" v-if=\"record.workType==1||record.workType==2\"/>\n          <a-popover trigger=\"click\" v-if=\"record.workType==1||record.workType==2\">\n            <template slot=\"content\">\n              <qrcode :value=\"url.shareUrl + record.id\" :size=\"250\"></qrcode>\n            </template>\n            <a>二维码</a>\n          </a-popover>\n          <a-divider type=\"vertical\" />\n          <a-popconfirm v-if=\"canDelete(record)\" title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n            <a>删除</a>\n          </a-popconfirm>\n          <a-tooltip v-else title=\"当前作品状态不允许删除\">\n            <a style=\"color: #ccc; cursor: not-allowed;\">删除</a>\n          </a-tooltip>\n        </span>\n      </a-table>\n    </div>\n    <!-- table区域-end -->\n\n    <teachingWorkPreview-modal ref=\"previewModal\"></teachingWorkPreview-modal>\n\n    <!-- 表单区域 -->\n    <!-- <teachingWork-modal ref=\"modalForm\" @ok=\"modalFormOk\"></teachingWork-modal> -->\n  </a-card>\n</template>\n\n<script>\n// import TeachingWorkModal from './modules/TeachingWorkModal'\nimport { postAction, getAction, deleteAction } from '@/api/manage'\nimport QrCode from '@/components/tools/QrCode'\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin'\nimport TeachingWorkPreviewModal from '@/views/teaching/modules/TeachingWorkPreviewModal'\nimport JDictSelectTag from '@/components/dict/JDictSelectTag.vue'\n\nexport default {\n  name: 'MineWorkList',\n  mixins: [JeecgListMixin],\n  components: {\n    qrcode: QrCode,\n    TeachingWorkPreviewModal,\n    JDictSelectTag\n  },\n  data() {\n    return {\n      description: '我的作品管理页面',\n      sendWorkId: null,\n      // 表头\n      columns: [\n        {\n          title: '#',\n          dataIndex: '',\n          key: 'rowIndex',\n          width: 60,\n          align: 'center',\n          customRender: function(t, r, index) {\n            return parseInt(index) + 1\n          }\n        },\n        {\n          title: '作品名',\n          align: 'center',\n          dataIndex: 'workName'\n        },\n        {\n          title: '封面',\n          align: 'center',\n          dataIndex: 'coverFileKey_url',\n          scopedSlots: { customRender: 'imgSlot' }\n        },\n        {\n          title: '观看数量',\n          align: 'center',\n          dataIndex: 'viewNum',\n          sorter: true,\n        },\n        {\n          title: '点赞数量',\n          align: 'center',\n          dataIndex: 'starNum',\n          sorter: true,\n        },\n        {\n          title: '作品状态',\n          align: 'center',\n          dataIndex: 'workStatus_dictText'\n        },\n        {\n          title: '创作来源',\n          align: 'center',\n          width: 100,\n          dataIndex: 'workScene',\n          sorter: true,\n          customRender(v){\n            switch(v){\n              case \"create\": return \"自由创作\"\n              case \"additional\": return \"班级作业\"\n              case \"course\": return \"课程作业\"\n              default: return v\n            }\n          }\n        },\n        {\n          title: '标签',\n          align: 'center',\n          dataIndex: 'workTag',\n          scopedSlots: {customRender: 'workTag'}\n        },\n        {\n          title: '得分/评语',\n          align: 'center',\n          dataIndex: 'score',\n          scopedSlots: { customRender: 'scoreInfo' }\n        },\n        {\n          title: '作品类型',\n          align: 'center',\n          dataIndex: 'workType_dictText'\n        },\n        {\n          title: '创作时间',\n          align: 'center',\n          dataIndex: 'createTime',\n          sorter: true,\n        },\n        {\n          title: '操作',\n          dataIndex: 'action',\n          align: 'center',\n          scopedSlots: { customRender: 'action' }\n        }\n      ],\n      url: {\n        list: '/teaching/teachingWork/mine',\n        delete: '/teaching/teachingWork/delete',\n        deleteBatch: '/teaching/teachingWork/deleteBatch',\n        shareUrl: window._CONFIG['webURL'] + \"/work-detail?id=\",\n      },\n      workTagValue: '',\n      workTag: []\n    }\n  },\n  computed: {},\n  created(){\n    this.getWorkTags()\n  },\n  methods: {\n    getWorkTags(){\n      getAction(\"/teaching/teachingWork/getWorkTags\").then(res=>{\n        this.workTag = res.result\n      })\n    },\n    setWorkTag(id){\n      getAction('/teaching/teachingWork/setWorkTag',{\n        workId: id,\n        workTag: this.workTagValue\n      }).then(res=>{\n        this.workTagValue = ''\n        this.$message.info(res.message)\n        this.loadData(1)\n        this.getWorkTags()\n      })\n    },\n    delWorkTag(e, tag){\n       e.preventDefault();\n       deleteAction('/teaching/teachingWork/delWorkTag', {tag}).then(res=>{\n         if(res.success){\n          this.getWorkTags()\n         }else{\n          if(confirm(res.message)){\n            deleteAction('/teaching/teachingWork/delWorkTag', {tag:tag, force:true}).then(res=>{\n              this.getWorkTags()\n            })\n          }\n         }\n        })\n    },\n    handlePreview(record){\n      this.$refs.previewModal.previewCode(record)\n    },\n    handleView: function(record) {\n      // 根据作业来源添加对应的参数\n      let additionalParams = '';\n      if (record.workScene === 'additional' && record.additionalId) {\n        additionalParams = '&scene=additional&additionalId=' + record.additionalId;\n      } else if (record.workScene === 'course' && record.courseId) {\n        additionalParams = '&scene=course&courseId=' + record.courseId;\n      }\n      \n      switch(record.workType){\n        case '1':\n          return window.open('/scratch3/index.html?workId=' + record.id + additionalParams)\n        case '2':\n          return window.open('/scratch3/index.html?workId=' + record.id + additionalParams)\n        case '3':\n          let mode = additionalParams ? '&mode=edit' : 'mode=edit';\n          return window.open('/scratchjr/editor.html?' + mode + '&workFile=' + record.workFileKey_url + additionalParams)\n        case '4':\n          return window.open('/python/index.html?workId=' + record.id + additionalParams)\n        case '5':  // 添加C++支持\n          return window.open('/cpp/index.html?workId=' + record.id + additionalParams)\n        case '10':\n          return window.open('/blockly/index.html?lang=zh-hans&workId=' + record.id + additionalParams)\n        default:\n          return window.open(record.workFileKey_url)\n      }\n    },\n    canDelete(record) {\n      // 1. 创作来源为\"自由创作\"的作品可以删除\n      if (record.workScene === \"create\") {\n        return true;\n      }\n      \n      // 2. 创作来源为\"课程作业\"或\"班级作业\"且作品状态为\"已保存\"的可以删除\n      if ((record.workScene === \"course\" || record.workScene === \"additional\") && \n          record.workStatus_dictText === \"已保存\") {\n        return true;\n      }\n      \n      // 其他情况不允许删除\n      return false;\n    }\n  }\n}\n</script>\n<style scoped>\n@import '~@assets/less/common.less';\n</style>"]}]}