<template>
  <div class="math-test-container">
    <a-card title="KaTeX数学公式渲染测试">
      <div class="test-section">
        <h3>行内公式测试</h3>
        <p>这是一个行内公式：<span v-math="'E = mc^2'"></span></p>
        <p>另一个行内公式：<span v-math="'\\frac{a}{b} + \\sqrt{x}'"></span></p>
      </div>

      <div class="test-section">
        <h3>块级公式测试</h3>
        <div v-math.display="'\\sum_{i=1}^{n} x_i = x_1 + x_2 + \\cdots + x_n'"></div>
        <div v-math.display="'\\int_{a}^{b} f(x) dx = F(b) - F(a)'"></div>
      </div>

      <div class="test-section">
        <h3>复杂公式测试</h3>
        <div v-math.display="'\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix} \\begin{pmatrix} x \\\\ y \\end{pmatrix} = \\begin{pmatrix} ax + by \\\\ cx + dy \\end{pmatrix}'"></div>
      </div>

      <div class="test-section">
        <h3>动态公式测试</h3>
        <a-input 
          v-model="customFormula" 
          placeholder="输入LaTeX公式，如：x^2 + y^2 = r^2"
          style="margin-bottom: 16px"
        />
        <div v-if="customFormula" v-math.display="customFormula"></div>
      </div>

      <div class="test-section">
        <h3>公式模板</h3>
        <a-row :gutter="16">
          <a-col :span="8" v-for="template in formulaTemplates" :key="template.name">
            <a-card size="small" :title="template.name" style="margin-bottom: 16px">
              <p>{{ template.description }}</p>
              <div v-math.display="template.formula"></div>
              <a-button size="small" @click="useTemplate(template.formula)">使用此模板</a-button>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script>
import { mathDirective, MathUtils } from '@/utils/mathRenderer'

export default {
  name: 'MathTest',
  directives: {
    math: mathDirective
  },
  data() {
    return {
      customFormula: 'x^2 + y^2 = r^2',
      formulaTemplates: []
    }
  },
  created() {
    this.formulaTemplates = MathUtils.getFormulaTemplates().slice(0, 6) // 只显示前6个模板
  },
  methods: {
    useTemplate(formula) {
      this.customFormula = formula
    }
  }
}
</script>

<style scoped>
.math-test-container {
  padding: 24px;
}

.test-section {
  margin-bottom: 32px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.test-section h3 {
  margin-bottom: 16px;
  color: #1890ff;
}

.test-section p {
  margin-bottom: 8px;
}

.test-section div[v-math] {
  margin: 16px 0;
  padding: 8px;
  background: white;
  border-radius: 4px;
  text-align: center;
}
</style>
