{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Index.vue?vue&type=style&index=0&id=05334a82&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\Index.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n.account-settings-info-main {\n  width: 100%;\n  display: flex;\n  height: 100%;\n  overflow: auto;\n\n  &.mobile {\n    display: block;\n\n    .account-settings-info-left {\n      border-right: unset;\n      border-bottom: 1px solid #e8e8e8;\n      width: 100%;\n      height: 50px;\n      overflow-x: auto;\n      overflow-y: scroll;\n    }\n    .account-settings-info-right {\n      padding: 20px 40px;\n    }\n  }\n\n  .account-settings-info-left {\n    border-right: 1px solid #e8e8e8;\n    width: 224px;\n  }\n\n  .account-settings-info-right {\n    flex: 1 1;\n    padding: 8px 40px;\n\n    .account-settings-info-title {\n      color: rgba(0,0,0,.85);\n      font-size: 20px;\n      font-weight: 500;\n      line-height: 28px;\n      margin-bottom: 12px;\n    }\n    .account-settings-info-view {\n      padding-top: 12px;\n    }\n  }\n}\n\n", {"version": 3, "sources": ["Index.vue"], "names": [], "mappings": ";AAiKA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Index.vue", "sourceRoot": "src/views/account/settings", "sourcesContent": ["<template>\n  <div class=\"page-header-index-wide\">\n    <a-card :bordered=\"false\" :bodyStyle=\"{ padding: '16px 0', height: '100%' }\" :style=\"{ height: '100%' }\">\n      <div class=\"account-settings-info-main\" :class=\"device\" :style=\" 'min-height:'+ mainInfoHeight \">\n        <div class=\"account-settings-info-left\">\n          <a-menu\n            :mode=\"device == 'mobile' ? 'horizontal' : 'inline'\"\n            :style=\"{ border: '0', width: device == 'mobile' ? '560px' : 'auto'}\"\n            :defaultSelectedKeys=\"defaultSelectedKeys\"\n            type=\"inner\"\n            @openChange=\"onOpenChange\"\n          >\n            <a-menu-item key=\"/account/settings/base\">\n              <router-link :to=\"{ name: 'account-settings-base' }\">\n                个人设置\n              </router-link>\n            </a-menu-item>\n            <a-menu-item key=\"/account/settings/password\">\n              <router-link :to=\"{ name: 'account-settings-password' }\">\n                修改密码\n              </router-link>\n            </a-menu-item>\n            <a-menu-item v-if=\"false\" key=\"/account/settings/security\">\n              <router-link :to=\"{ name: 'account-settings-security' }\">\n                安全设置\n              </router-link>\n            </a-menu-item>\n            <a-menu-item v-if=\"false\" key=\"/account/settings/custom\">\n              <router-link :to=\"{ name: 'account-settings-custom' }\">\n                个性化\n              </router-link>\n            </a-menu-item>\n            <a-menu-item v-if=\"false\" key=\"/account/settings/binding\">\n              <router-link :to=\"{ name: 'account-settings-binding' }\">\n                账户绑定\n              </router-link>\n            </a-menu-item>\n            <a-menu-item v-if=\"false\" key=\"/account/settings/notification\">\n              <router-link :to=\"{ name: 'account-settings-notification' }\">\n                新消息通知\n              </router-link>\n            </a-menu-item>\n          </a-menu>\n        </div>\n        <div class=\"account-settings-info-right\">\n          <div class=\"account-settings-info-title\">\n            <span>{{ $route.meta.title }}</span>\n          </div>\n          <route-view></route-view>\n        </div>\n      </div>\n    </a-card>\n  </div>\n</template>\n\n<script>\n  import PageLayout from '@/components/page/PageLayout'\n  import RouteView from \"@/components/layouts/RouteView\"\n  import { mixinDevice } from '@/utils/mixin.js'\n  import { mapGetters } from 'vuex'\n\n  export default {\n    components: {\n      RouteView,\n      PageLayout\n    },\n    mixins: [mixinDevice],\n    data () {\n      return {\n        // horizontal  inline\n        mode: 'inline',\n        mainInfoHeight:\"100%\",\n        openKeys: [],\n        defaultSelectedKeys: [],\n\n        // cropper\n        preview: {},\n        option: {\n          img: '/avatar2.jpg',\n          info: true,\n          size: 1,\n          outputType: 'jpeg',\n          canScale: false,\n          autoCrop: true,\n          // 只有自动截图开启 宽度高度才生效\n          autoCropWidth: 180,\n          autoCropHeight: 180,\n          fixedBox: true,\n          // 开启宽度和高度比例\n          fixed: true,\n          fixedNumber: [1, 1]\n        },\n\n        pageTitle: ''\n      }\n    },\n    created () {\n      this.updateMenu()\n    },\n    mounted(){\n      this.mainInfoHeight = (window.innerHeight-285)+\"px\";\n    },\n    methods: {\n      ...mapGetters(['userInfo']),\n      onOpenChange (openKeys) {\n        this.openKeys = openKeys\n      },\n      updateMenu () {\n        let routes = this.$route.matched.concat()\n        this.defaultSelectedKeys = [ routes.pop().path ]\n      }\n    },\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .account-settings-info-main {\n    width: 100%;\n    display: flex;\n    height: 100%;\n    overflow: auto;\n\n    &.mobile {\n      display: block;\n\n      .account-settings-info-left {\n        border-right: unset;\n        border-bottom: 1px solid #e8e8e8;\n        width: 100%;\n        height: 50px;\n        overflow-x: auto;\n        overflow-y: scroll;\n      }\n      .account-settings-info-right {\n        padding: 20px 40px;\n      }\n    }\n\n    .account-settings-info-left {\n      border-right: 1px solid #e8e8e8;\n      width: 224px;\n    }\n\n    .account-settings-info-right {\n      flex: 1 1;\n      padding: 8px 40px;\n\n      .account-settings-info-title {\n        color: rgba(0,0,0,.85);\n        font-size: 20px;\n        font-weight: 500;\n        line-height: 28px;\n        margin-bottom: 12px;\n      }\n      .account-settings-info-view {\n        padding-top: 12px;\n      }\n    }\n  }\n\n</style>"]}]}