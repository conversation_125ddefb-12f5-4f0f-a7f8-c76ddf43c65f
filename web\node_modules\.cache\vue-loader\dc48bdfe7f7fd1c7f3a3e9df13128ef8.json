{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\Trend\\Trend.vue?vue&type=style&index=0&id=32356f0b&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\Trend\\Trend.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n@import \"index\";\n", {"version": 3, "sources": ["Trend.vue"], "names": [], "mappings": ";AAyCA", "file": "Trend.vue", "sourceRoot": "src/components/Trend", "sourcesContent": ["<template>\n  <div :class=\"[prefixCls, reverseColor && 'reverse-color' ]\">\n    <span>\n      <slot name=\"term\"></slot>\n      <span class=\"item-text\">\n        <slot></slot>\n      </span>\n    </span>\n    <span :class=\"[flag]\"><a-icon :type=\"`caret-${flag}`\"/></span>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: \"Trend\",\n    props: {\n      prefixCls: {\n        type: String,\n        default: 'ant-pro-trend'\n      },\n      /**\n       * 上升下降标识：up|down\n       */\n      flag: {\n        type: String,\n        required: true\n      },\n      /**\n       * 颜色反转\n       */\n      reverseColor: {\n        type: Boolean,\n        default: false\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  @import \"index\";\n</style>"]}]}