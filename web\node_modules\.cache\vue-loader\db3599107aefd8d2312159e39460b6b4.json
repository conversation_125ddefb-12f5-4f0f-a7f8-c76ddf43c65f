{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\test\\MathTest.vue?vue&type=template&id=090d54e6&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\test\\MathTest.vue", "mtime": 1753759565930}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n<div class=\"math-test-container\">\n  <a-card title=\"KaTeX数学公式渲染测试\">\n    <div class=\"test-section\">\n      <h3>行内公式测试</h3>\n      <p>这是一个行内公式：<span v-math=\"'E = mc^2'\"></span></p>\n      <p>另一个行内公式：<span v-math=\"'\\\\frac{a}{b} + \\\\sqrt{x}'\"></span></p>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>块级公式测试</h3>\n      <div v-math.display=\"'\\\\sum_{i=1}^{n} x_i = x_1 + x_2 + \\\\cdots + x_n'\"></div>\n      <div v-math.display=\"'\\\\int_{a}^{b} f(x) dx = F(b) - F(a)'\"></div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>复杂公式测试</h3>\n      <div v-math.display=\"'\\\\begin{pmatrix} a & b \\\\\\\\ c & d \\\\end{pmatrix} \\\\begin{pmatrix} x \\\\\\\\ y \\\\end{pmatrix} = \\\\begin{pmatrix} ax + by \\\\\\\\ cx + dy \\\\end{pmatrix}'\"></div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>动态公式测试</h3>\n      <a-input \n        v-model=\"customFormula\" \n        placeholder=\"输入LaTeX公式，如：x^2 + y^2 = r^2\"\n        style=\"margin-bottom: 16px\"\n      />\n      <div v-if=\"customFormula\" v-math.display=\"customFormula\"></div>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>公式模板</h3>\n      <a-row :gutter=\"16\">\n        <a-col :span=\"8\" v-for=\"template in formulaTemplates\" :key=\"template.name\">\n          <a-card size=\"small\" :title=\"template.name\" style=\"margin-bottom: 16px\">\n            <p>{{ template.description }}</p>\n            <div v-math.display=\"template.formula\"></div>\n            <a-button size=\"small\" @click=\"useTemplate(template.formula)\">使用此模板</a-button>\n          </a-card>\n        </a-col>\n      </a-row>\n    </div>\n  </a-card>\n</div>\n", null]}