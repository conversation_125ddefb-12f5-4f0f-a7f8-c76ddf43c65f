{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseListCard.vue?vue&type=style&index=0&id=c5e139de&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\CourseListCard.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n.app-list {\n  .ant-card {\n    min-height: 200px;\n    max-height: 400px;\n    width: 400px;\n    display: inline-block;\n    margin: 20px;\n  }\n  .meta-cardInfo {\n    zoom: 1;\n    margin-top: 16px;\n    .title {\n      margin-right: 20px;\n    }\n    img {\n      width: 100%;\n      max-height: 200px;\n    }\n    > div {\n      position: relative;\n      text-align: left;\n      float: left;\n      width: 50%;\n\n      p {\n        line-height: 32px;\n        font-size: 24px;\n        margin: 0;\n\n        &:first-child {\n          color: rgba(0, 0, 0, 0.45);\n          font-size: 12px;\n          line-height: 20px;\n          margin-bottom: 4px;\n        }\n      }\n    }\n  }\n}\n", {"version": 3, "sources": ["CourseListCard.vue"], "names": [], "mappings": ";AA6EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CourseListCard.vue", "sourceRoot": "src/views/account/course", "sourcesContent": ["<template>\n  <div class=\"app-list\">\n    <a-card v-for=\"item in dataSource\" :key=\"item.id\" :hoverable=\"true\">\n      <!-- <template class=\"ant-card-extra\" slot=\"extra\">\n        <span class=\"create-time\">{{item.createTime}}</span>\n      </template> -->\n      <a-card-meta>\n        <div style=\"margin-bottom: 3px\" slot=\"title\">\n          <a-icon type=\"info-circle\" @click=\"toDetail(item)\"/>\n          <a-divider type=\"vertical\"></a-divider>\n          <span @click=\"toCourse(item.showType, item.id)\">{{ item.courseName }}</span>\n        </div>\n        <div @click=\"toCourse(item.showType, item.id)\" class=\"meta-cardInfo\" slot=\"description\">\n            <img\n              :src=\"getFileAccessHttpUrl(item.courseCover)\"\n              height=\"25px\"\n              style=\"width: 100%; height: 100%\"\n            />\n        </div>\n      </a-card-meta>\n    </a-card>\n    <j-modal \n      :visible=\"showCourseDetail\" \n      :title=\"currentCourse.courseName\"\n      :width=\"800\"\n      :footer=\"null\"\n      @cancel=\"showCourseDetail=false\"\n      >\n        <div v-html=\"currentCourse.courseDesc\"></div>\n      </j-modal>\n  </div>\n</template>\n<script>\nimport { getAction,getFileAccessHttpUrl } from '@/api/manage'\nimport {JeecgListMixin} from '@/mixins/JeecgListMixin'\nexport default {\n  name: 'MineCourseList',\n  components: {},\n  data() {\n    return {\n      dataSource: [],\n      showCourseDetail: false,\n      currentCourse: {}\n    }\n  },\n  mounted() {\n    this.getCourseList()\n  },\n  methods: {\n    getFileAccessHttpUrl,\n    getCourseList: function () {\n      getAction('/teaching/teachingCourse/mineCourse', {}).then((res) => {\n        if (res.success) {\n          this.dataSource = res.result\n        }\n        if (res.code === 510) {\n          this.$message.warning(res.message)\n        }\n        this.loading = false\n      })\n    },\n    toCourse(showType, id){\n      if(showType == 1){\n        this.$router.push(\"/teaching/mineCourse/courseUnitMap?id=\"+id)\n      }else{\n        this.$router.push(\"/teaching/mineCourse/courseUnitCard?id=\"+id)\n      }\n    },\n    toDetail(item) {\n      this.showCourseDetail = true\n      this.currentCourse = item\n    },\n  },\n}\n</script>\n\n<style lang=\"less\" scoped>\n.app-list {\n  .ant-card {\n    min-height: 200px;\n    max-height: 400px;\n    width: 400px;\n    display: inline-block;\n    margin: 20px;\n  }\n  .meta-cardInfo {\n    zoom: 1;\n    margin-top: 16px;\n    .title {\n      margin-right: 20px;\n    }\n    img {\n      width: 100%;\n      max-height: 200px;\n    }\n    > div {\n      position: relative;\n      text-align: left;\n      float: left;\n      width: 50%;\n\n      p {\n        line-height: 32px;\n        font-size: 24px;\n        margin: 0;\n\n        &:first-child {\n          color: rgba(0, 0, 0, 0.45);\n          font-size: 12px;\n          line-height: 20px;\n          margin-bottom: 4px;\n        }\n      }\n    }\n  }\n}\n</style>"]}]}