{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\form\\advancedForm\\AdvancedForm.vue?vue&type=style&index=0&id=7035909e&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\form\\advancedForm\\AdvancedForm.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745675069237}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.card{\n  margin-bottom: 24px;\n}\n", {"version": 3, "sources": ["AdvancedForm.vue"], "names": [], "mappings": ";AA8MA;AACA;AACA", "file": "AdvancedForm.vue", "sourceRoot": "src/views/form/advancedForm", "sourcesContent": ["<template>\n  <div>\n    <a-card class=\"card\" title=\"仓库管理\" :bordered=\"false\">\n      <repository-form ref=\"repository\" :showSubmit=\"false\" />\n    </a-card>\n    <a-card class=\"card\" title=\"任务管理\" :bordered=\"false\">\n      <task-form ref=\"task\" :showSubmit=\"false\" />\n    </a-card>\n\n    <!-- table -->\n    <a-card>\n      <form :autoFormCreate=\"(form) => this.form = form\">\n        <a-table\n          :columns=\"columns\"\n          :dataSource=\"data\"\n          :pagination=\"false\"\n        >\n          <template v-for=\"(col, i) in ['name', 'workId', 'department']\" :slot=\"col\" slot-scope=\"text, record, index\">\n            <a-input\n              :key=\"col\"\n              v-if=\"record.editable\"\n              style=\"margin: -5px 0\"\n              :value=\"text\"\n              :placeholder=\"columns[i].title\"\n              @change=\"e => handleChange(e.target.value, record.key, col)\"\n            />\n            <template v-else>{{ text }}</template>\n          </template>\n          <template slot=\"operation\" slot-scope=\"text, record, index\">\n            <template v-if=\"record.editable\">\n              <span v-if=\"record.isNew\">\n                <a @click=\"saveRow(record.key)\">添加</a>\n                <a-divider type=\"vertical\" />\n                <a-popconfirm title=\"是否要删除此行？\" @confirm=\"remove(record.key)\">\n                  <a>删除</a>\n                </a-popconfirm>\n              </span>\n              <span v-else>\n                <a @click=\"saveRow(record.key)\">保存</a>\n                <a-divider type=\"vertical\" />\n                <a @click=\"cancel(record.key)\">取消</a>\n              </span>\n            </template>\n            <span v-else>\n              <a @click=\"toggle(record.key)\">编辑</a>\n              <a-divider type=\"vertical\" />\n              <a-popconfirm title=\"是否要删除此行？\" @confirm=\"remove(record.key)\">\n                <a>删除</a>\n              </a-popconfirm>\n            </span>\n          </template>\n        </a-table>\n        <a-button style=\"width: 100%; margin-top: 16px; margin-bottom: 8px\" type=\"dashed\" icon=\"plus\" @click=\"newMember\">新增成员</a-button>\n      </form>\n    </a-card>\n\n    <!-- fixed footer toolbar -->\n    <footer-tool-bar>\n      <a-button type=\"primary\" @click=\"validate\" :loading=\"loading\">提交</a-button>\n    </footer-tool-bar>\n  </div>\n</template>\n\n<script>\n  import RepositoryForm from './RepositoryForm'\n  import TaskForm from './TaskForm'\n  import FooterToolBar from '@/components/tools/FooterToolBar'\n\n  export default {\n    name: \"AdvancedForm\",\n    components: {\n      FooterToolBar,\n      RepositoryForm,\n      TaskForm\n    },\n    data () {\n      return {\n        description: '高级表单常见于一次性输入和提交大批量数据的场景。',\n        loading: false,\n\n        // table\n        columns: [\n          {\n            title: '成员姓名',\n            dataIndex: 'name',\n            key: 'name',\n            width: '20%',\n            scopedSlots: { customRender: 'name' }\n          },\n          {\n            title: '工号',\n            dataIndex: 'workId',\n            key: 'workId',\n            width: '20%',\n            scopedSlots: { customRender: 'workId' }\n          },\n          {\n            title: '所属部门',\n            dataIndex: 'department',\n            key: 'department',\n            width: '40%',\n            scopedSlots: { customRender: 'department' }\n          },\n          {\n            title: '操作',\n            key: 'action',\n            scopedSlots: { customRender: 'operation' }\n          }\n        ],\n        data: [\n          {\n            key: '1',\n            name: '小明',\n            workId: '001',\n            editable: false,\n            department: '行政部'\n          },\n          {\n            key: '2',\n            name: '李莉',\n            workId: '002',\n            editable: false,\n            department: 'IT部'\n          },\n          {\n            key: '3',\n            name: '王小帅',\n            workId: '003',\n            editable: false,\n            department: '财务部'\n          }\n        ]\n      }\n    },\n    methods: {\n      handleSubmit (e) {\n        e.preventDefault()\n      },\n      newMember () {\n        this.data.push({\n          key: '-1',\n          name: '',\n          workId: '',\n          department: '',\n          editable: true,\n          isNew: true\n        })\n      },\n      remove (key) {\n        const newData = this.data.filter(item => item.key !== key)\n        this.data = newData\n      },\n      saveRow (key) {\n        let target = this.data.filter(item => item.key === key)[0]\n        target.editable = false\n        target.isNew = false\n      },\n      toggle (key) {\n        let target = this.data.filter(item => item.key === key)[0]\n        target.editable = !target.editable\n      },\n      getRowByKey (key, newData) {\n        const data = this.data\n        return (newData || data).filter(item => item.key === key)[0]\n      },\n      cancel (key) {\n        let target = this.data.filter(item => item.key === key)[0]\n        target.editable = false\n      },\n      handleChange (value, key, column) {\n        const newData = [...this.data]\n        const target = newData.filter(item => key === item.key)[0]\n        if (target) {\n          target[column] = value\n          this.data = newData\n        }\n      },\n\n      // 最终全页面提交\n      validate () {\n        this.$refs.repository.form.validateFields((err, values) => {\n          if (!err) {\n            this.$notification['error']({\n              message: 'Received values of form:',\n              description: values\n            })\n          }\n        })\n        this.$refs.task.form.validateFields((err, values) => {\n          if (!err) {\n            this.$notification['error']({\n              message: 'Received values of form:',\n              description: values\n            })\n          }\n        })\n      }\n    }\n  }\n</script>\n\n<style lang=\"less\" scoped>\n  .card{\n    margin-bottom: 24px;\n  }\n</style>"]}]}