{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\test\\ComprehensiveTestSuite.vue?vue&type=style&index=0&id=e2939ff0&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\examSystem\\test\\ComprehensiveTestSuite.vue", "mtime": 1753520887545}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.comprehensive-test-suite {\n  padding: 24px;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n.test-control-panel {\n  background: #fafafa;\n  padding: 16px;\n  border-radius: 6px;\n}\n\n.test-actions {\n  text-align: right;\n}\n\n.suite-selected {\n  border: 2px solid #1890ff;\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);\n}\n\n.ant-statistic-group {\n  display: flex;\n  justify-content: space-around;\n  background: white;\n  padding: 16px;\n  border-radius: 6px;\n  border: 1px solid #e8e8e8;\n}\n\n.test-chart,\n.performance-chart {\n  background: #fafafa;\n  padding: 16px;\n  border-radius: 6px;\n}\n\n.performance-metrics {\n  display: flex;\n  align-items: center;\n}\n\n.performance-metrics p {\n  margin-bottom: 8px;\n}\n\n.ant-card {\n  margin-bottom: 16px;\n}\n\n.ant-card-head-title {\n  font-weight: 600;\n}\n", {"version": 3, "sources": ["ComprehensiveTestSuite.vue"], "names": [], "mappings": ";AAosBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "ComprehensiveTestSuite.vue", "sourceRoot": "src/views/examSystem/test", "sourcesContent": ["<template>\n  <div class=\"comprehensive-test-suite\">\n    <a-card title=\"阶段五：全面功能测试 - 综合测试套件\" style=\"margin-bottom: 16px;\">\n      <a-alert\n        message=\"综合测试说明\"\n        description=\"此页面提供完整的系统测试功能，包括功能测试、性能测试和用户体验测试。通过自动化测试确保系统的稳定性、性能和用户体验。\"\n        type=\"info\"\n        show-icon\n        style=\"margin-bottom: 16px;\"\n      />\n      \n      <!-- 测试控制面板 -->\n      <div class=\"test-control-panel\" style=\"margin-bottom: 24px;\">\n        <a-row :gutter=\"16\">\n          <a-col :span=\"12\">\n            <a-statistic-group>\n              <a-statistic title=\"测试套件\" :value=\"testSuiteCount\" />\n              <a-statistic title=\"测试用例\" :value=\"totalTestCases\" />\n              <a-statistic title=\"已完成\" :value=\"completedTests\" />\n              <a-statistic title=\"成功率\" :value=\"successRate\" suffix=\"%\" />\n            </a-statistic-group>\n          </a-col>\n          <a-col :span=\"12\">\n            <div class=\"test-actions\">\n              <a-button \n                type=\"primary\" \n                size=\"large\"\n                :loading=\"isRunning\"\n                :disabled=\"isRunning\"\n                @click=\"runAllTests\"\n                icon=\"play-circle\">\n                运行全部测试\n              </a-button>\n              <a-button \n                :disabled=\"!hasResults\"\n                @click=\"exportReport\"\n                icon=\"download\"\n                style=\"margin-left: 8px;\">\n                导出报告\n              </a-button>\n              <a-button \n                :disabled=\"!isRunning\"\n                @click=\"stopTests\"\n                icon=\"stop\"\n                style=\"margin-left: 8px;\">\n                停止测试\n              </a-button>\n              <a-button \n                :disabled=\"!hasResults\"\n                @click=\"clearResults\"\n                icon=\"clear\"\n                style=\"margin-left: 8px;\">\n                清空结果\n              </a-button>\n            </div>\n          </a-col>\n        </a-row>\n      </div>\n\n      <!-- 测试进度 -->\n      <div v-if=\"isRunning\" class=\"test-progress\" style=\"margin-bottom: 24px;\">\n        <a-card title=\"测试进度\" size=\"small\">\n          <a-progress \n            :percent=\"testProgress.progress\" \n            :status=\"testProgress.status\"\n            :strokeWidth=\"8\"\n          />\n          <div style=\"margin-top: 12px;\">\n            <a-tag color=\"blue\">{{ testProgress.phase }}</a-tag>\n            <span style=\"margin-left: 8px;\">{{ testProgress.message }}</span>\n          </div>\n          <div v-if=\"testProgress.currentSuite\" style=\"margin-top: 8px;\">\n            <strong>当前套件：</strong>{{ testProgress.currentSuite }}\n          </div>\n        </a-card>\n      </div>\n    </a-card>\n\n    <!-- 测试套件选择 -->\n    <a-card title=\"测试套件配置\" style=\"margin-bottom: 16px;\">\n      <a-row :gutter=\"16\">\n        <a-col :span=\"8\">\n          <a-card size=\"small\" :class=\"{ 'suite-selected': selectedSuites.includes('functional') }\">\n            <template slot=\"title\">\n              <a-checkbox \n                v-model=\"selectedSuites\" \n                value=\"functional\"\n                @change=\"updateTestCaseCount\">\n                功能测试套件\n              </a-checkbox>\n            </template>\n            <p>测试系统核心功能的正确性和完整性</p>\n            <ul style=\"font-size: 12px; color: #666;\">\n              <li>图片支持测试</li>\n              <li>数学公式测试</li>\n              <li>导入导出测试</li>\n              <li>兼容性测试</li>\n            </ul>\n            <a-tag color=\"green\">{{ functionalTestCount }} 个测试用例</a-tag>\n          </a-card>\n        </a-col>\n        <a-col :span=\"8\">\n          <a-card size=\"small\" :class=\"{ 'suite-selected': selectedSuites.includes('performance') }\">\n            <template slot=\"title\">\n              <a-checkbox \n                v-model=\"selectedSuites\" \n                value=\"performance\"\n                @change=\"updateTestCaseCount\">\n                性能测试套件\n              </a-checkbox>\n            </template>\n            <p>测试系统各组件的性能表现</p>\n            <ul style=\"font-size: 12px; color: #666;\">\n              <li>图片加载性能</li>\n              <li>编辑器性能</li>\n              <li>数据处理性能</li>\n              <li>内存使用监控</li>\n            </ul>\n            <a-tag color=\"orange\">{{ performanceTestCount }} 个测试用例</a-tag>\n          </a-card>\n        </a-col>\n        <a-col :span=\"8\">\n          <a-card size=\"small\" :class=\"{ 'suite-selected': selectedSuites.includes('userExperience') }\">\n            <template slot=\"title\">\n              <a-checkbox \n                v-model=\"selectedSuites\" \n                value=\"userExperience\"\n                @change=\"updateTestCaseCount\">\n                用户体验测试套件\n              </a-checkbox>\n            </template>\n            <p>测试系统的用户界面和交互体验</p>\n            <ul style=\"font-size: 12px; color: #666;\">\n              <li>界面流畅性</li>\n              <li>响应式设计</li>\n              <li>交互体验</li>\n              <li>可访问性</li>\n            </ul>\n            <a-tag color=\"purple\">{{ userExperienceTestCount }} 个测试用例</a-tag>\n          </a-card>\n        </a-col>\n      </a-row>\n    </a-card>\n\n    <!-- 测试结果概览 -->\n    <a-card v-if=\"hasResults\" title=\"测试结果概览\" style=\"margin-bottom: 16px;\">\n      <a-row :gutter=\"16\">\n        <a-col :span=\"6\">\n          <a-statistic \n            title=\"总测试数\" \n            :value=\"testSummary.totalTests\"\n            :value-style=\"{ color: '#1890ff' }\"\n          />\n        </a-col>\n        <a-col :span=\"6\">\n          <a-statistic \n            title=\"通过测试\" \n            :value=\"testSummary.totalPassed\"\n            :value-style=\"{ color: '#52c41a' }\"\n          />\n        </a-col>\n        <a-col :span=\"6\">\n          <a-statistic \n            title=\"失败测试\" \n            :value=\"testSummary.totalFailed\"\n            :value-style=\"{ color: '#ff4d4f' }\"\n          />\n        </a-col>\n        <a-col :span=\"6\">\n          <a-statistic \n            title=\"跳过测试\" \n            :value=\"testSummary.totalSkipped\"\n            :value-style=\"{ color: '#faad14' }\"\n          />\n        </a-col>\n      </a-row>\n\n      <!-- 测试结果图表 -->\n      <div style=\"margin-top: 24px;\">\n        <a-row :gutter=\"16\">\n          <a-col :span=\"12\">\n            <div class=\"test-chart\">\n              <h4>测试结果分布</h4>\n              <div class=\"result-chart\" ref=\"resultChart\" style=\"height: 200px;\"></div>\n            </div>\n          </a-col>\n          <a-col :span=\"12\">\n            <div class=\"performance-chart\">\n              <h4>性能指标</h4>\n              <div class=\"performance-metrics\">\n                <a-progress \n                  type=\"circle\" \n                  :percent=\"Math.round(testSummary.successRate)\"\n                  :status=\"testSummary.successRate >= 80 ? 'success' : 'exception'\"\n                  :width=\"80\"\n                />\n                <div style=\"margin-left: 16px;\">\n                  <p><strong>总耗时：</strong>{{ formatDuration(testSummary.totalDuration) }}</p>\n                  <p><strong>平均耗时：</strong>{{ formatDuration(testSummary.totalDuration / testSummary.totalTests) }}</p>\n                  <p><strong>性能评分：</strong>{{ getPerformanceScore() }}</p>\n                </div>\n              </div>\n            </div>\n          </a-col>\n        </a-row>\n      </div>\n    </a-card>\n\n    <!-- 详细测试结果 -->\n    <a-card v-if=\"hasResults\" title=\"详细测试结果\">\n      <a-tabs>\n        <a-tab-pane \n          v-for=\"(suiteResult, suiteName) in testResults\" \n          :key=\"suiteName\" \n          :tab=\"`${getSuiteDisplayName(suiteName)} (${suiteResult.passed}/${suiteResult.tests.length})`\">\n          \n          <!-- 套件概览 -->\n          <div style=\"margin-bottom: 16px;\">\n            <a-descriptions bordered size=\"small\">\n              <a-descriptions-item label=\"套件状态\">\n                <a-tag :color=\"suiteResult.status === 'passed' ? 'green' : 'red'\">\n                  {{ suiteResult.status === 'passed' ? '通过' : '失败' }}\n                </a-tag>\n              </a-descriptions-item>\n              <a-descriptions-item label=\"执行时间\">{{ formatDuration(suiteResult.duration) }}</a-descriptions-item>\n              <a-descriptions-item label=\"通过率\">{{ Math.round((suiteResult.passed / suiteResult.tests.length) * 100) }}%</a-descriptions-item>\n            </a-descriptions>\n          </div>\n\n          <!-- 测试用例列表 -->\n          <a-table\n            :dataSource=\"suiteResult.tests\"\n            :columns=\"testCaseColumns\"\n            :pagination=\"{ pageSize: 10 }\"\n            size=\"small\"\n            rowKey=\"name\">\n            \n            <template slot=\"status\" slot-scope=\"text, record\">\n              <a-tag :color=\"getStatusColor(record.status)\">\n                {{ getStatusText(record.status) }}\n              </a-tag>\n            </template>\n            \n            <template slot=\"duration\" slot-scope=\"text\">\n              {{ formatDuration(text) }}\n            </template>\n            \n            <template slot=\"metrics\" slot-scope=\"text, record\">\n              <a-button \n                v-if=\"Object.keys(record.metrics).length > 0\"\n                type=\"link\" \n                size=\"small\"\n                @click=\"showMetricsDetail(record)\">\n                查看指标\n              </a-button>\n            </template>\n            \n            <template slot=\"error\" slot-scope=\"text\">\n              <a-tooltip v-if=\"text\" :title=\"text\">\n                <a-icon type=\"exclamation-circle\" style=\"color: #ff4d4f;\" />\n              </a-tooltip>\n            </template>\n          </a-table>\n        </a-tab-pane>\n      </a-tabs>\n    </a-card>\n\n    <!-- 性能问题和建议 -->\n    <a-card v-if=\"hasResults && (testSummary.performanceIssues.length > 0 || testSummary.recommendations.length > 0)\" title=\"问题和建议\">\n      <a-row :gutter=\"16\">\n        <a-col :span=\"12\" v-if=\"testSummary.performanceIssues.length > 0\">\n          <h4>性能问题</h4>\n          <a-list\n            :dataSource=\"testSummary.performanceIssues\"\n            size=\"small\">\n            <a-list-item slot=\"renderItem\" slot-scope=\"issue\">\n              <a-list-item-meta>\n                <template slot=\"title\">\n                  <a-tag color=\"orange\">{{ issue.type }}</a-tag>\n                  {{ issue.test }}\n                </template>\n                <template slot=\"description\">\n                  {{ issue.message }}\n                </template>\n              </a-list-item-meta>\n            </a-list-item>\n          </a-list>\n        </a-col>\n        <a-col :span=\"12\" v-if=\"testSummary.recommendations.length > 0\">\n          <h4>优化建议</h4>\n          <a-list\n            :dataSource=\"testSummary.recommendations\"\n            size=\"small\">\n            <a-list-item slot=\"renderItem\" slot-scope=\"recommendation\">\n              <a-list-item-meta>\n                <template slot=\"title\">\n                  <a-tag :color=\"getPriorityColor(recommendation.priority)\">\n                    {{ recommendation.priority }}\n                  </a-tag>\n                  {{ recommendation.type }}\n                </template>\n                <template slot=\"description\">\n                  {{ recommendation.message }}\n                </template>\n              </a-list-item-meta>\n            </a-list-item>\n          </a-list>\n        </a-col>\n      </a-row>\n    </a-card>\n\n    <!-- 指标详情模态框 -->\n    <a-modal\n      v-model=\"metricsModalVisible\"\n      title=\"测试指标详情\"\n      :footer=\"null\"\n      width=\"600px\">\n      <div v-if=\"selectedTestMetrics\">\n        <h4>{{ selectedTestMetrics.name }}</h4>\n        <a-descriptions bordered size=\"small\">\n          <a-descriptions-item \n            v-for=\"(value, key) in selectedTestMetrics.metrics\" \n            :key=\"key\"\n            :label=\"formatMetricLabel(key)\">\n            {{ formatMetricValue(key, value) }}\n          </a-descriptions-item>\n        </a-descriptions>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script>\nimport TestManager from '@/utils/testManager'\nimport FunctionalTestSuite from '@/utils/testSuites/functionalTestSuite'\nimport PerformanceTestSuite from '@/utils/testSuites/performanceTestSuite'\nimport UserExperienceTestSuite from '@/utils/testSuites/userExperienceTestSuite'\n\nexport default {\n  name: 'ComprehensiveTestSuite',\n  data() {\n    return {\n      // 测试管理器\n      testManager: new TestManager(),\n      \n      // 测试套件\n      functionalSuite: new FunctionalTestSuite(),\n      performanceSuite: new PerformanceTestSuite(),\n      userExperienceSuite: new UserExperienceTestSuite(),\n      \n      // 选中的测试套件\n      selectedSuites: ['functional', 'performance', 'userExperience'],\n      \n      // 测试状态\n      isRunning: false,\n      hasResults: false,\n      \n      // 测试进度\n      testProgress: {\n        progress: 0,\n        status: 'normal',\n        phase: '',\n        message: '',\n        currentSuite: null\n      },\n      \n      // 测试结果\n      testResults: {},\n      testSummary: {\n        totalSuites: 0,\n        totalTests: 0,\n        totalPassed: 0,\n        totalFailed: 0,\n        totalSkipped: 0,\n        totalDuration: 0,\n        successRate: 0,\n        performanceIssues: [],\n        recommendations: []\n      },\n      \n      // 测试用例数量\n      functionalTestCount: 0,\n      performanceTestCount: 0,\n      userExperienceTestCount: 0,\n      \n      // 表格列定义\n      testCaseColumns: [\n        {\n          title: '测试用例',\n          dataIndex: 'name',\n          key: 'name',\n          width: 200\n        },\n        {\n          title: '描述',\n          dataIndex: 'description',\n          key: 'description'\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          width: 80,\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '耗时',\n          dataIndex: 'duration',\n          key: 'duration',\n          width: 80,\n          scopedSlots: { customRender: 'duration' }\n        },\n        {\n          title: '指标',\n          key: 'metrics',\n          width: 80,\n          scopedSlots: { customRender: 'metrics' }\n        },\n        {\n          title: '错误',\n          dataIndex: 'error',\n          key: 'error',\n          width: 60,\n          scopedSlots: { customRender: 'error' }\n        }\n      ],\n      \n      // 指标详情模态框\n      metricsModalVisible: false,\n      selectedTestMetrics: null\n    }\n  },\n  computed: {\n    testSuiteCount() {\n      return this.selectedSuites.length\n    },\n    \n    totalTestCases() {\n      let total = 0\n      if (this.selectedSuites.includes('functional')) total += this.functionalTestCount\n      if (this.selectedSuites.includes('performance')) total += this.performanceTestCount\n      if (this.selectedSuites.includes('userExperience')) total += this.userExperienceTestCount\n      return total\n    },\n    \n    completedTests() {\n      return this.testSummary.totalTests\n    },\n    \n    successRate() {\n      return Math.round(this.testSummary.successRate) || 0\n    }\n  },\n  mounted() {\n    this.initializeTestSuites()\n    this.updateTestCaseCount()\n  },\n  methods: {\n    // 初始化测试套件\n    initializeTestSuites() {\n      // 注册测试套件\n      this.testManager.registerTestSuite('functional', this.functionalSuite)\n      this.testManager.registerTestSuite('performance', this.performanceSuite)\n      this.testManager.registerTestSuite('userExperience', this.userExperienceSuite)\n      \n      // 获取测试用例数量\n      this.functionalTestCount = this.functionalSuite.getTestCases().length\n      this.performanceTestCount = this.performanceSuite.getTestCases().length\n      this.userExperienceTestCount = this.userExperienceSuite.getTestCases().length\n    },\n    \n    // 更新测试用例数量\n    updateTestCaseCount() {\n      // 这个方法在选择套件时调用，用于更新计算属性\n      this.$forceUpdate()\n    },\n    \n    // 运行所有测试\n    async runAllTests() {\n      if (this.selectedSuites.length === 0) {\n        this.$message.warning('请至少选择一个测试套件')\n        return\n      }\n      \n      this.isRunning = true\n      this.hasResults = false\n      this.testResults = {}\n      this.testSummary = {\n        totalSuites: 0,\n        totalTests: 0,\n        totalPassed: 0,\n        totalFailed: 0,\n        totalSkipped: 0,\n        totalDuration: 0,\n        successRate: 0,\n        performanceIssues: [],\n        recommendations: []\n      }\n      \n      try {\n        // 清理之前的结果\n        this.testManager.clearResults()\n        \n        // 只注册选中的测试套件\n        const tempManager = new TestManager()\n        if (this.selectedSuites.includes('functional')) {\n          tempManager.registerTestSuite('functional', this.functionalSuite)\n        }\n        if (this.selectedSuites.includes('performance')) {\n          tempManager.registerTestSuite('performance', this.performanceSuite)\n        }\n        if (this.selectedSuites.includes('userExperience')) {\n          tempManager.registerTestSuite('userExperience', this.userExperienceSuite)\n        }\n        \n        // 运行测试\n        const result = await tempManager.runAllTests({}, this.handleTestProgress)\n        \n        if (result.success) {\n          this.testResults = result.results\n          this.testSummary = result.summary\n          this.hasResults = true\n          this.$message.success('所有测试已完成')\n          \n          // 渲染图表\n          this.$nextTick(() => {\n            this.renderResultChart()\n          })\n        } else {\n          this.$message.error('测试运行失败: ' + result.error)\n        }\n      } catch (error) {\n        this.$message.error('测试运行异常: ' + error.message)\n      } finally {\n        this.isRunning = false\n        this.testProgress = {\n          progress: 0,\n          status: 'normal',\n          phase: '',\n          message: '',\n          currentSuite: null\n        }\n      }\n    },\n    \n    // 处理测试进度\n    handleTestProgress(progress) {\n      this.testProgress = {\n        ...progress,\n        status: progress.phase === 'completed' ? 'success' : 'active'\n      }\n    },\n    \n    // 停止测试\n    stopTests() {\n      this.testManager.stopTests()\n      this.isRunning = false\n      this.$message.info('测试已停止')\n    },\n    \n    // 清空结果\n    clearResults() {\n      this.hasResults = false\n      this.testResults = {}\n      this.testSummary = {\n        totalSuites: 0,\n        totalTests: 0,\n        totalPassed: 0,\n        totalFailed: 0,\n        totalSkipped: 0,\n        totalDuration: 0,\n        successRate: 0,\n        performanceIssues: [],\n        recommendations: []\n      }\n      this.testManager.clearResults()\n      this.$message.success('测试结果已清空')\n    },\n    \n    // 导出报告\n    exportReport() {\n      try {\n        const reportContent = this.testManager.exportReport('html')\n        const blob = new Blob([reportContent], { type: 'text/html;charset=utf-8' })\n        const url = window.URL.createObjectURL(blob)\n        const link = document.createElement('a')\n        link.href = url\n        link.download = `测试报告_${new Date().toISOString().slice(0, 10)}.html`\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n        \n        this.$message.success('测试报告已导出')\n      } catch (error) {\n        this.$message.error('导出失败: ' + error.message)\n      }\n    },\n    \n    // 显示指标详情\n    showMetricsDetail(testCase) {\n      this.selectedTestMetrics = testCase\n      this.metricsModalVisible = true\n    },\n    \n    // 渲染结果图表\n    renderResultChart() {\n      // 这里可以集成图表库（如ECharts）来渲染测试结果图表\n      // 为了简化，这里只是一个占位方法\n      console.log('渲染测试结果图表')\n    },\n    \n    // 获取套件显示名称\n    getSuiteDisplayName(suiteName) {\n      const names = {\n        functional: '功能测试',\n        performance: '性能测试',\n        userExperience: '用户体验'\n      }\n      return names[suiteName] || suiteName\n    },\n    \n    // 获取状态颜色\n    getStatusColor(status) {\n      const colors = {\n        passed: 'green',\n        failed: 'red',\n        skipped: 'orange',\n        running: 'blue'\n      }\n      return colors[status] || 'default'\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const texts = {\n        passed: '通过',\n        failed: '失败',\n        skipped: '跳过',\n        running: '运行中'\n      }\n      return texts[status] || status\n    },\n    \n    // 获取优先级颜色\n    getPriorityColor(priority) {\n      const colors = {\n        high: 'red',\n        medium: 'orange',\n        low: 'blue'\n      }\n      return colors[priority] || 'default'\n    },\n    \n    // 格式化持续时间\n    formatDuration(ms) {\n      if (ms < 1000) {\n        return `${Math.round(ms)}ms`\n      } else if (ms < 60000) {\n        return `${(ms / 1000).toFixed(1)}s`\n      } else {\n        return `${(ms / 60000).toFixed(1)}min`\n      }\n    },\n    \n    // 格式化指标标签\n    formatMetricLabel(key) {\n      const labels = {\n        executionTime: '执行时间',\n        memoryUsage: '内存使用',\n        renderTime: '渲染时间',\n        loadTime: '加载时间',\n        responseTime: '响应时间',\n        fileSize: '文件大小',\n        imageCount: '图片数量',\n        formulaCount: '公式数量'\n      }\n      return labels[key] || key\n    },\n    \n    // 格式化指标值\n    formatMetricValue(key, value) {\n      if (key.includes('Time') || key.includes('Duration')) {\n        return this.formatDuration(value)\n      } else if (key.includes('Size')) {\n        return `${Math.round(value / 1024)}KB`\n      } else if (typeof value === 'number') {\n        return value.toLocaleString()\n      } else {\n        return value\n      }\n    },\n    \n    // 获取性能评分\n    getPerformanceScore() {\n      const successRate = this.testSummary.successRate\n      const issueCount = this.testSummary.performanceIssues.length\n      \n      let score = successRate\n      score -= issueCount * 5 // 每个性能问题扣5分\n      \n      return Math.max(0, Math.round(score))\n    }\n  }\n}\n</script>\n\n<style scoped>\n.comprehensive-test-suite {\n  padding: 24px;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n.test-control-panel {\n  background: #fafafa;\n  padding: 16px;\n  border-radius: 6px;\n}\n\n.test-actions {\n  text-align: right;\n}\n\n.suite-selected {\n  border: 2px solid #1890ff;\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);\n}\n\n.ant-statistic-group {\n  display: flex;\n  justify-content: space-around;\n  background: white;\n  padding: 16px;\n  border-radius: 6px;\n  border: 1px solid #e8e8e8;\n}\n\n.test-chart,\n.performance-chart {\n  background: #fafafa;\n  padding: 16px;\n  border-radius: 6px;\n}\n\n.performance-metrics {\n  display: flex;\n  align-items: center;\n}\n\n.performance-metrics p {\n  margin-bottom: 8px;\n}\n\n.ant-card {\n  margin-bottom: 16px;\n}\n\n.ant-card-head-title {\n  font-weight: 600;\n}\n</style>\n"]}]}