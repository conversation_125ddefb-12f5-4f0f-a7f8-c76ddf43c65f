{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\modules\\UnitViewModal.vue", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\course\\modules\\UnitViewModal.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./UnitViewModal.vue?vue&type=template&id=7fb21df8&scoped=true\"\nimport script from \"./UnitViewModal.vue?vue&type=script&lang=js\"\nexport * from \"./UnitViewModal.vue?vue&type=script&lang=js\"\nimport style0 from \"./UnitViewModal.vue?vue&type=style&index=0&id=7fb21df8&lang=less&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7fb21df8\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"E:\\\\teachingproject\\\\teaching\\\\web\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('7fb21df8')) {\n      api.createRecord('7fb21df8', component.options)\n    } else {\n      api.reload('7fb21df8', component.options)\n    }\n    module.hot.accept(\"./UnitViewModal.vue?vue&type=template&id=7fb21df8&scoped=true\", function () {\n      api.rerender('7fb21df8', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/account/course/modules/UnitViewModal.vue\"\nexport default component.exports"]}