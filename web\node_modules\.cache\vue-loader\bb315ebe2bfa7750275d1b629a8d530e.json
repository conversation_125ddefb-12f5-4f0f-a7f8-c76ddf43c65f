{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\AvatarModal.vue?vue&type=style&index=0&id=********&lang=less&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\settings\\AvatarModal.vue", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n\n.avatar-upload-preview {\n  position: absolute;\n  top: 50%;\n  transform: translate(50%, -50%);\n  width: 180px;\n  height: 180px;\n  border-radius: 50%;\n  box-shadow: 0 0 4px #ccc;\n  overflow: hidden;\n\n  img {\n    width: 100%;\n    height: 100%;\n  }\n}\n", {"version": 3, "sources": ["AvatarModal.vue"], "names": [], "mappings": ";;AAwGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "AvatarModal.vue", "sourceRoot": "src/views/account/settings", "sourcesContent": ["<template>\n  <a-modal :visible=\"visible\" title=\"修改头像\" :maskClosable=\"false\" :confirmLoading=\"confirmLoading\" :width=\"800\">\n    <a-row>\n      <a-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n        <vue-cropper\n          ref=\"cropper\"\n          :img=\"options.img\"\n          :info=\"true\"\n          :autoCrop=\"options.autoCrop\"\n          :autoCropWidth=\"options.autoCropWidth\"\n          :autoCropHeight=\"options.autoCropHeight\"\n          :fixedBox=\"options.fixedBox\"\n          @realTime=\"realTime\"\n        >\n        </vue-cropper>\n      </a-col>\n      <a-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n        <div class=\"avatar-upload-preview\">\n          <img :src=\"previews.url\" :style=\"previews.img\"/>\n        </div>\n      </a-col>\n    </a-row>\n\n    <template slot=\"footer\">\n      <a-button key=\"back\" @click=\"cancelHandel\">取消</a-button>\n      <a-button key=\"submit\" type=\"primary\" :loading=\"confirmLoading\" @click=\"okHandel\">保存</a-button>\n    </template>\n  </a-modal>\n</template>\n<script>\n  import { VueCropper } from 'vue-cropper'\n\n  export default {\n    components: {\n      VueCropper\n    },\n    data() {\n      return {\n        visible: false,\n        id: null,\n        confirmLoading: false,\n\n        options: {\n          img: '/avatar2.jpg',\n          autoCrop: true,\n          autoCropWidth: 200,\n          autoCropHeight: 200,\n          fixedBox: true\n        },\n        previews: {},\n      };\n    },\n    methods: {\n      edit(id) {\n        this.visible = true;\n        this.id = id;\n        /* 获取原始头像 */\n\n      },\n      close() {\n        this.id = null;\n        this.visible = false;\n      },\n      cancelHandel() {\n        this.close();\n      },\n      okHandel() {\n        const vm = this\n\n        vm.confirmLoading = true\n        setTimeout(() => {\n          vm.confirmLoading = false\n          vm.close()\n          vm.$message.success('上传头像成功');\n        }, 2000)\n\n      },\n\n      realTime(data) {\n        this.previews = data\n      }\n    }\n  };\n</script>\n\n<style lang=\"less\" scoped>\n\n  .avatar-upload-preview {\n    position: absolute;\n    top: 50%;\n    transform: translate(50%, -50%);\n    width: 180px;\n    height: 180px;\n    border-radius: 50%;\n    box-shadow: 0 0 4px #ccc;\n    overflow: hidden;\n\n    img {\n      width: 100%;\n      height: 100%;\n    }\n  }\n</style>"]}]}