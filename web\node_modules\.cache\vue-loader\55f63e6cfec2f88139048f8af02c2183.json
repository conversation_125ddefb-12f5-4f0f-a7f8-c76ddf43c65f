{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\test\\MathTest.vue?vue&type=style&index=0&id=090d54e6&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\test\\MathTest.vue", "mtime": 1753759565930}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.math-test-container {\n  padding: 24px;\n}\n\n.test-section {\n  margin-bottom: 32px;\n  padding: 16px;\n  border: 1px solid #f0f0f0;\n  border-radius: 6px;\n  background: #fafafa;\n}\n\n.test-section h3 {\n  margin-bottom: 16px;\n  color: #1890ff;\n}\n\n.test-section p {\n  margin-bottom: 8px;\n}\n\n.test-section div[v-math] {\n  margin: 16px 0;\n  padding: 8px;\n  background: white;\n  border-radius: 4px;\n  text-align: center;\n}\n", {"version": 3, "sources": ["MathTest.vue"], "names": [], "mappings": ";AAwEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MathTest.vue", "sourceRoot": "src/views/test", "sourcesContent": ["<template>\n  <div class=\"math-test-container\">\n    <a-card title=\"KaTeX数学公式渲染测试\">\n      <div class=\"test-section\">\n        <h3>行内公式测试</h3>\n        <p>这是一个行内公式：<span v-math=\"'E = mc^2'\"></span></p>\n        <p>另一个行内公式：<span v-math=\"'\\\\frac{a}{b} + \\\\sqrt{x}'\"></span></p>\n      </div>\n\n      <div class=\"test-section\">\n        <h3>块级公式测试</h3>\n        <div v-math.display=\"'\\\\sum_{i=1}^{n} x_i = x_1 + x_2 + \\\\cdots + x_n'\"></div>\n        <div v-math.display=\"'\\\\int_{a}^{b} f(x) dx = F(b) - F(a)'\"></div>\n      </div>\n\n      <div class=\"test-section\">\n        <h3>复杂公式测试</h3>\n        <div v-math.display=\"'\\\\begin{pmatrix} a & b \\\\\\\\ c & d \\\\end{pmatrix} \\\\begin{pmatrix} x \\\\\\\\ y \\\\end{pmatrix} = \\\\begin{pmatrix} ax + by \\\\\\\\ cx + dy \\\\end{pmatrix}'\"></div>\n      </div>\n\n      <div class=\"test-section\">\n        <h3>动态公式测试</h3>\n        <a-input \n          v-model=\"customFormula\" \n          placeholder=\"输入LaTeX公式，如：x^2 + y^2 = r^2\"\n          style=\"margin-bottom: 16px\"\n        />\n        <div v-if=\"customFormula\" v-math.display=\"customFormula\"></div>\n      </div>\n\n      <div class=\"test-section\">\n        <h3>公式模板</h3>\n        <a-row :gutter=\"16\">\n          <a-col :span=\"8\" v-for=\"template in formulaTemplates\" :key=\"template.name\">\n            <a-card size=\"small\" :title=\"template.name\" style=\"margin-bottom: 16px\">\n              <p>{{ template.description }}</p>\n              <div v-math.display=\"template.formula\"></div>\n              <a-button size=\"small\" @click=\"useTemplate(template.formula)\">使用此模板</a-button>\n            </a-card>\n          </a-col>\n        </a-row>\n      </div>\n    </a-card>\n  </div>\n</template>\n\n<script>\nimport { mathDirective, MathUtils } from '@/utils/mathRenderer'\n\nexport default {\n  name: 'MathTest',\n  directives: {\n    math: mathDirective\n  },\n  data() {\n    return {\n      customFormula: 'x^2 + y^2 = r^2',\n      formulaTemplates: []\n    }\n  },\n  created() {\n    this.formulaTemplates = MathUtils.getFormulaTemplates().slice(0, 6) // 只显示前6个模板\n  },\n  methods: {\n    useTemplate(formula) {\n      this.customFormula = formula\n    }\n  }\n}\n</script>\n\n<style scoped>\n.math-test-container {\n  padding: 24px;\n}\n\n.test-section {\n  margin-bottom: 32px;\n  padding: 16px;\n  border: 1px solid #f0f0f0;\n  border-radius: 6px;\n  background: #fafafa;\n}\n\n.test-section h3 {\n  margin-bottom: 16px;\n  color: #1890ff;\n}\n\n.test-section p {\n  margin-bottom: 8px;\n}\n\n.test-section div[v-math] {\n  margin: 16px 0;\n  padding: 8px;\n  background: white;\n  border-radius: 4px;\n  text-align: center;\n}\n</style>\n"]}]}