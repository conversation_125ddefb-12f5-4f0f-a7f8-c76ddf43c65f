{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--12-0!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\views\\test\\MathTest.vue?vue&type=template&id=090d54e6&scoped=true", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\test\\MathTest.vue", "mtime": 1753759565930}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"math-test-container\"\n  }, [_c(\"a-card\", {\n    attrs: {\n      title: \"KaTeX数学公式渲染测试\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"test-section\"\n  }, [_c(\"h3\", [_vm._v(\"行内公式测试\")]), _c(\"p\", [_vm._v(\"这是一个行内公式：\"), _c(\"span\", {\n    directives: [{\n      name: \"math\",\n      rawName: \"v-math\",\n      value: \"E = mc^2\",\n      expression: \"'E = mc^2'\"\n    }]\n  })]), _c(\"p\", [_vm._v(\"另一个行内公式：\"), _c(\"span\", {\n    directives: [{\n      name: \"math\",\n      rawName: \"v-math\",\n      value: \"\\\\frac{a}{b} + \\\\sqrt{x}\",\n      expression: \"'\\\\\\\\frac{a}{b} + \\\\\\\\sqrt{x}'\"\n    }]\n  })])]), _c(\"div\", {\n    staticClass: \"test-section\"\n  }, [_c(\"h3\", [_vm._v(\"块级公式测试\")]), _c(\"div\", {\n    directives: [{\n      name: \"math\",\n      rawName: \"v-math.display\",\n      value: \"\\\\sum_{i=1}^{n} x_i = x_1 + x_2 + \\\\cdots + x_n\",\n      expression: \"'\\\\\\\\sum_{i=1}^{n} x_i = x_1 + x_2 + \\\\\\\\cdots + x_n'\",\n      modifiers: {\n        display: true\n      }\n    }]\n  }), _c(\"div\", {\n    directives: [{\n      name: \"math\",\n      rawName: \"v-math.display\",\n      value: \"\\\\int_{a}^{b} f(x) dx = F(b) - F(a)\",\n      expression: \"'\\\\\\\\int_{a}^{b} f(x) dx = F(b) - F(a)'\",\n      modifiers: {\n        display: true\n      }\n    }]\n  })]), _c(\"div\", {\n    staticClass: \"test-section\"\n  }, [_c(\"h3\", [_vm._v(\"复杂公式测试\")]), _c(\"div\", {\n    directives: [{\n      name: \"math\",\n      rawName: \"v-math.display\",\n      value: \"\\\\begin{pmatrix} a & b \\\\\\\\ c & d \\\\end{pmatrix} \\\\begin{pmatrix} x \\\\\\\\ y \\\\end{pmatrix} = \\\\begin{pmatrix} ax + by \\\\\\\\ cx + dy \\\\end{pmatrix}\",\n      expression: \"'\\\\\\\\begin{pmatrix} a & b \\\\\\\\\\\\\\\\ c & d \\\\\\\\end{pmatrix} \\\\\\\\begin{pmatrix} x \\\\\\\\\\\\\\\\ y \\\\\\\\end{pmatrix} = \\\\\\\\begin{pmatrix} ax + by \\\\\\\\\\\\\\\\ cx + dy \\\\\\\\end{pmatrix}'\",\n      modifiers: {\n        display: true\n      }\n    }]\n  })]), _c(\"div\", {\n    staticClass: \"test-section\"\n  }, [_c(\"h3\", [_vm._v(\"动态公式测试\")]), _c(\"a-input\", {\n    staticStyle: {\n      \"margin-bottom\": \"16px\"\n    },\n    attrs: {\n      placeholder: \"输入LaTeX公式，如：x^2 + y^2 = r^2\"\n    },\n    model: {\n      value: _vm.customFormula,\n      callback: function callback($$v) {\n        _vm.customFormula = $$v;\n      },\n      expression: \"customFormula\"\n    }\n  }), _vm.customFormula ? _c(\"div\", {\n    directives: [{\n      name: \"math\",\n      rawName: \"v-math.display\",\n      value: _vm.customFormula,\n      expression: \"customFormula\",\n      modifiers: {\n        display: true\n      }\n    }]\n  }) : _vm._e()], 1), _c(\"div\", {\n    staticClass: \"test-section\"\n  }, [_c(\"h3\", [_vm._v(\"公式模板\")]), _c(\"a-row\", {\n    attrs: {\n      gutter: 16\n    }\n  }, _vm._l(_vm.formulaTemplates, function (template) {\n    return _c(\"a-col\", {\n      key: template.name,\n      attrs: {\n        span: 8\n      }\n    }, [_c(\"a-card\", {\n      staticStyle: {\n        \"margin-bottom\": \"16px\"\n      },\n      attrs: {\n        size: \"small\",\n        title: template.name\n      }\n    }, [_c(\"p\", [_vm._v(_vm._s(template.description))]), _c(\"div\", {\n      directives: [{\n        name: \"math\",\n        rawName: \"v-math.display\",\n        value: template.formula,\n        expression: \"template.formula\",\n        modifiers: {\n          display: true\n        }\n      }]\n    }), _c(\"a-button\", {\n      attrs: {\n        size: \"small\"\n      },\n      on: {\n        click: function click($event) {\n          return _vm.useTemplate(template.formula);\n        }\n      }\n    }, [_vm._v(\"使用此模板\")])], 1)], 1);\n  }), 1)], 1)])], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "title", "_v", "directives", "name", "rawName", "value", "expression", "modifiers", "display", "staticStyle", "placeholder", "model", "customFormula", "callback", "$$v", "_e", "gutter", "_l", "formulaTemplates", "template", "key", "span", "size", "_s", "description", "formula", "on", "click", "$event", "useTemplate", "staticRenderFns", "_withStripped"], "sources": ["E:/teachingproject/teaching/web/src/views/test/MathTest.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"math-test-container\" },\n    [\n      _c(\"a-card\", { attrs: { title: \"KaTeX数学公式渲染测试\" } }, [\n        _c(\"div\", { staticClass: \"test-section\" }, [\n          _c(\"h3\", [_vm._v(\"行内公式测试\")]),\n          _c(\"p\", [\n            _vm._v(\"这是一个行内公式：\"),\n            _c(\"span\", {\n              directives: [\n                {\n                  name: \"math\",\n                  rawName: \"v-math\",\n                  value: \"E = mc^2\",\n                  expression: \"'E = mc^2'\",\n                },\n              ],\n            }),\n          ]),\n          _c(\"p\", [\n            _vm._v(\"另一个行内公式：\"),\n            _c(\"span\", {\n              directives: [\n                {\n                  name: \"math\",\n                  rawName: \"v-math\",\n                  value: \"\\\\frac{a}{b} + \\\\sqrt{x}\",\n                  expression: \"'\\\\\\\\frac{a}{b} + \\\\\\\\sqrt{x}'\",\n                },\n              ],\n            }),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"test-section\" }, [\n          _c(\"h3\", [_vm._v(\"块级公式测试\")]),\n          _c(\"div\", {\n            directives: [\n              {\n                name: \"math\",\n                rawName: \"v-math.display\",\n                value: \"\\\\sum_{i=1}^{n} x_i = x_1 + x_2 + \\\\cdots + x_n\",\n                expression:\n                  \"'\\\\\\\\sum_{i=1}^{n} x_i = x_1 + x_2 + \\\\\\\\cdots + x_n'\",\n                modifiers: { display: true },\n              },\n            ],\n          }),\n          _c(\"div\", {\n            directives: [\n              {\n                name: \"math\",\n                rawName: \"v-math.display\",\n                value: \"\\\\int_{a}^{b} f(x) dx = F(b) - F(a)\",\n                expression: \"'\\\\\\\\int_{a}^{b} f(x) dx = F(b) - F(a)'\",\n                modifiers: { display: true },\n              },\n            ],\n          }),\n        ]),\n        _c(\"div\", { staticClass: \"test-section\" }, [\n          _c(\"h3\", [_vm._v(\"复杂公式测试\")]),\n          _c(\"div\", {\n            directives: [\n              {\n                name: \"math\",\n                rawName: \"v-math.display\",\n                value:\n                  \"\\\\begin{pmatrix} a & b \\\\\\\\ c & d \\\\end{pmatrix} \\\\begin{pmatrix} x \\\\\\\\ y \\\\end{pmatrix} = \\\\begin{pmatrix} ax + by \\\\\\\\ cx + dy \\\\end{pmatrix}\",\n                expression:\n                  \"'\\\\\\\\begin{pmatrix} a & b \\\\\\\\\\\\\\\\ c & d \\\\\\\\end{pmatrix} \\\\\\\\begin{pmatrix} x \\\\\\\\\\\\\\\\ y \\\\\\\\end{pmatrix} = \\\\\\\\begin{pmatrix} ax + by \\\\\\\\\\\\\\\\ cx + dy \\\\\\\\end{pmatrix}'\",\n                modifiers: { display: true },\n              },\n            ],\n          }),\n        ]),\n        _c(\n          \"div\",\n          { staticClass: \"test-section\" },\n          [\n            _c(\"h3\", [_vm._v(\"动态公式测试\")]),\n            _c(\"a-input\", {\n              staticStyle: { \"margin-bottom\": \"16px\" },\n              attrs: { placeholder: \"输入LaTeX公式，如：x^2 + y^2 = r^2\" },\n              model: {\n                value: _vm.customFormula,\n                callback: function ($$v) {\n                  _vm.customFormula = $$v\n                },\n                expression: \"customFormula\",\n              },\n            }),\n            _vm.customFormula\n              ? _c(\"div\", {\n                  directives: [\n                    {\n                      name: \"math\",\n                      rawName: \"v-math.display\",\n                      value: _vm.customFormula,\n                      expression: \"customFormula\",\n                      modifiers: { display: true },\n                    },\n                  ],\n                })\n              : _vm._e(),\n          ],\n          1\n        ),\n        _c(\n          \"div\",\n          { staticClass: \"test-section\" },\n          [\n            _c(\"h3\", [_vm._v(\"公式模板\")]),\n            _c(\n              \"a-row\",\n              { attrs: { gutter: 16 } },\n              _vm._l(_vm.formulaTemplates, function (template) {\n                return _c(\n                  \"a-col\",\n                  { key: template.name, attrs: { span: 8 } },\n                  [\n                    _c(\n                      \"a-card\",\n                      {\n                        staticStyle: { \"margin-bottom\": \"16px\" },\n                        attrs: { size: \"small\", title: template.name },\n                      },\n                      [\n                        _c(\"p\", [_vm._v(_vm._s(template.description))]),\n                        _c(\"div\", {\n                          directives: [\n                            {\n                              name: \"math\",\n                              rawName: \"v-math.display\",\n                              value: template.formula,\n                              expression: \"template.formula\",\n                              modifiers: { display: true },\n                            },\n                          ],\n                        }),\n                        _c(\n                          \"a-button\",\n                          {\n                            attrs: { size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.useTemplate(template.formula)\n                              },\n                            },\n                          },\n                          [_vm._v(\"使用此模板\")]\n                        ),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                )\n              }),\n              1\n            ),\n          ],\n          1\n        ),\n      ]),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAsB,CAAC,EACtC,CACEF,EAAE,CAAC,QAAQ,EAAE;IAAEG,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAgB;EAAE,CAAC,EAAE,CAClDJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BL,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACM,EAAE,CAAC,WAAW,CAAC,EACnBL,EAAE,CAAC,MAAM,EAAE;IACTM,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAE,UAAU;MACjBC,UAAU,EAAE;IACd,CAAC;EAEL,CAAC,CAAC,CACH,CAAC,EACFV,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACM,EAAE,CAAC,UAAU,CAAC,EAClBL,EAAE,CAAC,MAAM,EAAE;IACTM,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAE,0BAA0B;MACjCC,UAAU,EAAE;IACd,CAAC;EAEL,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BL,EAAE,CAAC,KAAK,EAAE;IACRM,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,gBAAgB;MACzBC,KAAK,EAAE,iDAAiD;MACxDC,UAAU,EACR,uDAAuD;MACzDC,SAAS,EAAE;QAAEC,OAAO,EAAE;MAAK;IAC7B,CAAC;EAEL,CAAC,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IACRM,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,gBAAgB;MACzBC,KAAK,EAAE,qCAAqC;MAC5CC,UAAU,EAAE,yCAAyC;MACrDC,SAAS,EAAE;QAAEC,OAAO,EAAE;MAAK;IAC7B,CAAC;EAEL,CAAC,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BL,EAAE,CAAC,KAAK,EAAE;IACRM,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,gBAAgB;MACzBC,KAAK,EACH,kJAAkJ;MACpJC,UAAU,EACR,4KAA4K;MAC9KC,SAAS,EAAE;QAAEC,OAAO,EAAE;MAAK;IAC7B,CAAC;EAEL,CAAC,CAAC,CACH,CAAC,EACFZ,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5BL,EAAE,CAAC,SAAS,EAAE;IACZa,WAAW,EAAE;MAAE,eAAe,EAAE;IAAO,CAAC;IACxCV,KAAK,EAAE;MAAEW,WAAW,EAAE;IAA8B,CAAC;IACrDC,KAAK,EAAE;MACLN,KAAK,EAAEV,GAAG,CAACiB,aAAa;MACxBC,QAAQ,EAAE,SAAAA,SAAUC,GAAG,EAAE;QACvBnB,GAAG,CAACiB,aAAa,GAAGE,GAAG;MACzB,CAAC;MACDR,UAAU,EAAE;IACd;EACF,CAAC,CAAC,EACFX,GAAG,CAACiB,aAAa,GACbhB,EAAE,CAAC,KAAK,EAAE;IACRM,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,gBAAgB;MACzBC,KAAK,EAAEV,GAAG,CAACiB,aAAa;MACxBN,UAAU,EAAE,eAAe;MAC3BC,SAAS,EAAE;QAAEC,OAAO,EAAE;MAAK;IAC7B,CAAC;EAEL,CAAC,CAAC,GACFb,GAAG,CAACoB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,EACDnB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BL,EAAE,CACA,OAAO,EACP;IAAEG,KAAK,EAAE;MAAEiB,MAAM,EAAE;IAAG;EAAE,CAAC,EACzBrB,GAAG,CAACsB,EAAE,CAACtB,GAAG,CAACuB,gBAAgB,EAAE,UAAUC,QAAQ,EAAE;IAC/C,OAAOvB,EAAE,CACP,OAAO,EACP;MAAEwB,GAAG,EAAED,QAAQ,CAAChB,IAAI;MAAEJ,KAAK,EAAE;QAAEsB,IAAI,EAAE;MAAE;IAAE,CAAC,EAC1C,CACEzB,EAAE,CACA,QAAQ,EACR;MACEa,WAAW,EAAE;QAAE,eAAe,EAAE;MAAO,CAAC;MACxCV,KAAK,EAAE;QAAEuB,IAAI,EAAE,OAAO;QAAEtB,KAAK,EAAEmB,QAAQ,CAAChB;MAAK;IAC/C,CAAC,EACD,CACEP,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAACN,GAAG,CAAC4B,EAAE,CAACJ,QAAQ,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC,EAC/C5B,EAAE,CAAC,KAAK,EAAE;MACRM,UAAU,EAAE,CACV;QACEC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,gBAAgB;QACzBC,KAAK,EAAEc,QAAQ,CAACM,OAAO;QACvBnB,UAAU,EAAE,kBAAkB;QAC9BC,SAAS,EAAE;UAAEC,OAAO,EAAE;QAAK;MAC7B,CAAC;IAEL,CAAC,CAAC,EACFZ,EAAE,CACA,UAAU,EACV;MACEG,KAAK,EAAE;QAAEuB,IAAI,EAAE;MAAQ,CAAC;MACxBI,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,MAAUC,MAAM,EAAE;UACvB,OAAOjC,GAAG,CAACkC,WAAW,CAACV,QAAQ,CAACM,OAAO,CAAC;QAC1C;MACF;IACF,CAAC,EACD,CAAC9B,GAAG,CAACM,EAAE,CAAC,OAAO,CAAC,CAClB,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAI6B,eAAe,GAAG,EAAE;AACxBpC,MAAM,CAACqC,aAAa,GAAG,IAAI;AAE3B,SAASrC,MAAM,EAAEoC,eAAe", "ignoreList": []}]}